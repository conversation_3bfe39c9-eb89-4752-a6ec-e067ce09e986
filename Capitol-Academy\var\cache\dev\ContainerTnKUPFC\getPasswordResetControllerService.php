<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getPasswordResetControllerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the public 'App\Controller\PasswordResetController' shared autowired service.
     *
     * @return \App\Controller\PasswordResetController
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'framework-bundle'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AbstractController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'PasswordResetController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'PasswordResetService.php';

        $a = ($container->privates['App\\Repository\\UserRepository'] ?? $container->load('getUserRepositoryService'));
        $b = ($container->privates['App\\Service\\IpAddressService'] ?? $container->load('getIpAddressServiceService'));

        $container->services['App\\Controller\\PasswordResetController'] = $instance = new \App\Controller\PasswordResetController(new \App\Service\PasswordResetService(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['App\\Repository\\PasswordResetTokenRepository'] ?? $container->load('getPasswordResetTokenRepositoryService')), $a, ($container->privates['mailer.mailer'] ?? $container->load('getMailer_MailerService')), ($container->privates['parameter_bag'] ??= new \Symfony\Component\DependencyInjection\ParameterBag\ContainerBag($container)), ($container->privates['monolog.logger'] ?? self::getMonolog_LoggerService($container)), $b), $b, ($container->privates['security.csrf.token_manager'] ?? $container->load('getSecurity_Csrf_TokenManagerService')), ($container->privates['debug.validator'] ?? self::getDebug_ValidatorService($container)), $a);

        $instance->setContainer(($container->privates['.service_locator.O2p6Lk7'] ?? $container->load('get_ServiceLocator_O2p6Lk7Service'))->withContext('App\\Controller\\PasswordResetController', $container));

        return $instance;
    }
}
