<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_MCbvPMWService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.mCbvPMW' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.mCbvPMW'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'admin' => ['privates', '.errored..service_locator.mCbvPMW.App\\Entity\\Admin', NULL, 'Cannot autowire service ".service_locator.mCbvPMW": it needs an instance of "App\\Entity\\Admin" but this type has been excluded in "config/services.yaml".'],
        ], [
            'admin' => 'App\\Entity\\Admin',
        ]);
    }
}
