<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getAdminControllerService extends App_KernelDevDebugContainer
{
    /**
     * Gets the public 'App\Controller\AdminController' shared autowired service.
     *
     * @return \App\Controller\AdminController
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'vendor'.\DIRECTORY_SEPARATOR.'symfony'.\DIRECTORY_SEPARATOR.'framework-bundle'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AbstractController.php';
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Controller'.\DIRECTORY_SEPARATOR.'AdminController.php';

        $container->services['App\\Controller\\AdminController'] = $instance = new \App\Controller\AdminController(($container->services['doctrine.orm.default_entity_manager'] ?? self::getDoctrine_Orm_DefaultEntityManagerService($container)), ($container->privates['App\\Repository\\UserRepository'] ?? $container->load('getUserRepositoryService')), ($container->privates['App\\Repository\\CourseRepository'] ?? $container->load('getCourseRepositoryService')), ($container->privates['App\\Repository\\PlanRepository'] ?? $container->load('getPlanRepositoryService')), ($container->privates['App\\Repository\\CategoryRepository'] ?? $container->load('getCategoryRepositoryService')), ($container->privates['App\\Repository\\ContactRepository'] ?? $container->load('getContactRepositoryService')), ($container->privates['App\\Repository\\AdminRepository'] ?? $container->load('getAdminRepositoryService')), ($container->privates['App\\Repository\\PartnerRepository'] ?? self::getPartnerRepositoryService($container)), ($container->privates['App\\Repository\\PromotionalBannerRepository'] ?? self::getPromotionalBannerRepositoryService($container)), ($container->privates['App\\Service\\AdminPermissionService'] ?? $container->load('getAdminPermissionServiceService')), ($container->privates['App\\Service\\EmailUniquenessValidator'] ?? $container->load('getEmailUniquenessValidatorService')), ($container->privates['App\\Service\\IpAddressService'] ?? $container->load('getIpAddressServiceService')), ($container->privates['App\\Service\\ValidationService'] ?? $container->load('getValidationServiceService')), ($container->privates['App\\Service\\ErrorHandlingService'] ?? $container->load('getErrorHandlingServiceService')), ($container->privates['debug.validator'] ?? self::getDebug_ValidatorService($container)), ($container->privates['monolog.logger'] ?? self::getMonolog_LoggerService($container)));

        $instance->setContainer(($container->privates['.service_locator.O2p6Lk7'] ?? $container->load('get_ServiceLocator_O2p6Lk7Service'))->withContext('App\\Controller\\AdminController', $container));

        return $instance;
    }
}
