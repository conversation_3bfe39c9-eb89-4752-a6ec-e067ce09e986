<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* cart/index.html.twig */
class __TwigTemplate_8a33a2e7b4f166147ea3ce1b32ee8c4c extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "cart/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "cart/index.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Shopping Cart - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 6
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        .cart-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }

        .cart-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(1, 26, 45, 0.1);
            overflow: hidden;
            border: 1px solid rgba(1, 26, 45, 0.05);
        }

        .cart-header {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .cart-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/></svg>') repeat;
            opacity: 0.3;
        }

        .cart-header h1 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .cart-header p {
            font-family: 'Calibri', Arial, sans-serif;
            opacity: 0.9;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .cart-content {
            padding: 50px 40px;
        }

        .cart-item {
            border: 2px solid #f1f3f4;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .cart-item:hover {
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.1);
            border-color: #e9ecef;
            background: white;
            transform: translateY(-2px);
        }

        .item-thumbnail {
            width: 80px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
        }

        .item-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            background: #a90418;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .item-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #a90418;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: #011a2d;
            color: white;
            border-color: #011a2d;
        }

        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 5px;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 0.85rem;
            transition: background 0.3s ease;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        .cart-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 35px;
            position: sticky;
            top: 100px;
            box-shadow: 0 10px 30px rgba(1, 26, 45, 0.08);
        }

        .summary-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 25px;
            font-size: 1.3rem;
            text-align: center;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            font-family: 'Calibri', Arial, sans-serif;
        }

        .summary-row.total {
            border-top: 3px solid #011a2d;
            padding-top: 20px;
            margin-top: 20px;
            font-size: 1.3rem;
            font-weight: 700;
            color: #011a2d;
            font-family: 'Montserrat', sans-serif;
        }

        .checkout-btn {
            background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);
            color: white;
            border: none;
            padding: 18px 30px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            font-family: 'Montserrat', sans-serif;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 20px;
        }

        .checkout-btn:hover {
            background: linear-gradient(135deg, #8b0314 0%, #a90418 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(169, 4, 24, 0.3);
        }

        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .empty-cart {
            text-align: center;
            padding: 80px 20px;
            color: #6c757d;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            margin: 20px;
        }

        .empty-cart i {
            font-size: 5rem;
            margin-bottom: 25px;
            color: #011a2d;
            opacity: 0.3;
        }

        .empty-cart h3 {
            font-family: 'Montserrat', sans-serif;
            color: #011a2d;
            margin-bottom: 15px;
        }

        .empty-cart p {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .continue-shopping {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .continue-shopping:hover {
            background: #0d2a42;
            color: white;
            text-decoration: none;
        }

        .alert-removed-items {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .cart-content {
                padding: 20px;
            }
            
            .cart-item {
                padding: 15px;
            }
            
            .item-thumbnail {
                width: 60px;
                height: 45px;
            }
            
            .quantity-controls {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 299
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 300
        yield "    <section class=\"cart-section\">
        <div class=\"container\">
            <div class=\"cart-container\">
                <div class=\"cart-header\">
                    <h1 class=\"mb-3\">
                        <i class=\"fas fa-shopping-cart me-3\"></i>Shopping Cart
                    </h1>
                    <p class=\"mb-0\">Review your selected items before checkout</p>
                </div>

                <div class=\"cart-content\">
                    ";
        // line 311
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["removed_items"]) || array_key_exists("removed_items", $context) ? $context["removed_items"] : (function () { throw new RuntimeError('Variable "removed_items" does not exist.', 311, $this->source); })())) > 0)) {
            // line 312
            yield "                        <div class=\"alert-removed-items\">
                            <h6><i class=\"fas fa-exclamation-triangle me-2\"></i>Items Removed</h6>
                            <p class=\"mb-0\">
                                ";
            // line 315
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["removed_items"]) || array_key_exists("removed_items", $context) ? $context["removed_items"] : (function () { throw new RuntimeError('Variable "removed_items" does not exist.', 315, $this->source); })())), "html", null, true);
            yield " item(s) were removed from your cart because they are no longer available.
                            </p>
                        </div>
                    ";
        }
        // line 319
        yield "
                    ";
        // line 320
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 320, $this->source); })()), "items", [], "any", false, false, false, 320)) > 0)) {
            // line 321
            yield "                        <div class=\"row\">
                            <div class=\"col-lg-8\">
                                <div class=\"cart-items\">
                                    ";
            // line 324
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 324, $this->source); })()), "items", [], "any", false, false, false, 324));
            foreach ($context['_seq'] as $context["item_key"] => $context["item"]) {
                // line 325
                yield "                                        <div class=\"cart-item\" data-item-key=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($context["item_key"], "html", null, true);
                yield "\">
                                            <div class=\"row align-items-center\">
                                                <div class=\"col-md-2\">
                                                    <div class=\"item-thumbnail\">
                                                        ";
                // line 329
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["item"], "thumbnail", [], "any", false, false, false, 329)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 330
                    yield "                                                            <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(((("uploads/" . CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 330)) . "s/thumbnails/") . CoreExtension::getAttribute($this->env, $this->source, $context["item"], "thumbnail", [], "any", false, false, false, 330))), "html", null, true);
                    yield "\" 
                                                                 alt=\"";
                    // line 331
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 331), "html", null, true);
                    yield "\">
                                                        ";
                } else {
                    // line 333
                    yield "                                                            <div class=\"d-flex align-items-center justify-content-center h-100\">
                                                                <i class=\"fas fa-play text-white\"></i>
                                                            </div>
                                                        ";
                }
                // line 337
                yield "                                                    </div>
                                                </div>
                                                
                                                <div class=\"col-md-4\">
                                                    <div class=\"item-info\">
                                                        <h6>";
                // line 342
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 342), "html", null, true);
                yield "</h6>
                                                        <span class=\"item-type\">";
                // line 343
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), Twig\Extension\CoreExtension::replace(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 343), ["_" => " "])), "html", null, true);
                yield "</span>
                                                    </div>
                                                </div>
                                                
                                                <div class=\"col-md-2\">
                                                    <div class=\"item-price\">
                                                        \$";
                // line 349
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "price", [], "any", false, false, false, 349), 2), "html", null, true);
                yield "
                                                    </div>
                                                </div>
                                                
                                                <div class=\"col-md-2\">
                                                    <div class=\"quantity-controls\">
                                                        <button class=\"quantity-btn\" onclick=\"updateQuantity('";
                // line 355
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 355), "html", null, true);
                yield "', ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "id", [], "any", false, false, false, 355), "html", null, true);
                yield ", ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 355) - 1), "html", null, true);
                yield ")\">
                                                            <i class=\"fas fa-minus\"></i>
                                                        </button>
                                                        <input type=\"number\" 
                                                               class=\"quantity-input\" 
                                                               value=\"";
                // line 360
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 360), "html", null, true);
                yield "\" 
                                                               min=\"1\" 
                                                               onchange=\"updateQuantity('";
                // line 362
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 362), "html", null, true);
                yield "', ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "id", [], "any", false, false, false, 362), "html", null, true);
                yield ", this.value)\">
                                                        <button class=\"quantity-btn\" onclick=\"updateQuantity('";
                // line 363
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 363), "html", null, true);
                yield "', ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "id", [], "any", false, false, false, 363), "html", null, true);
                yield ", ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((CoreExtension::getAttribute($this->env, $this->source, $context["item"], "quantity", [], "any", false, false, false, 363) + 1), "html", null, true);
                yield ")\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <div class=\"col-md-2 text-end\">
                                                    <button class=\"remove-btn\" onclick=\"removeItem('";
                // line 370
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 370), "html", null, true);
                yield "', ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "id", [], "any", false, false, false, 370), "html", null, true);
                yield ")\">
                                                        <i class=\"fas fa-trash me-1\"></i>Remove
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['item_key'], $context['item'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 377
            yield "                                </div>

                                <div class=\"mt-4\">
                                    <a href=\"";
            // line 380
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos_list");
            yield "\" class=\"continue-shopping\">
                                        <i class=\"fas fa-arrow-left me-2\"></i>Continue Shopping
                                    </a>
                                    
                                    <button class=\"btn btn-outline-danger ms-3\" onclick=\"clearCart()\">
                                        <i class=\"fas fa-trash me-2\"></i>Clear Cart
                                    </button>
                                </div>
                            </div>

                            <div class=\"col-lg-4\">
                                <div class=\"cart-summary\">
                                    <h5 class=\"summary-title\">Order Summary</h5>
                                    
                                    <div class=\"summary-row\">
                                        <span>Items (";
            // line 395
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 395, $this->source); })()), "total_items", [], "any", false, false, false, 395), "html", null, true);
            yield ")</span>
                                        <span>\$";
            // line 396
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 396, $this->source); })()), "subtotal", [], "any", false, false, false, 396), 2), "html", null, true);
            yield "</span>
                                    </div>
                                    
                                    <div class=\"summary-row\">
                                        <span>Tax</span>
                                        <span>\$";
            // line 401
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 401, $this->source); })()), "tax", [], "any", false, false, false, 401), 2), "html", null, true);
            yield "</span>
                                    </div>
                                    
                                    <div class=\"summary-row total\">
                                        <span>Total</span>
                                        <span>\$";
            // line 406
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart"]) || array_key_exists("cart", $context) ? $context["cart"] : (function () { throw new RuntimeError('Variable "cart" does not exist.', 406, $this->source); })()), "total", [], "any", false, false, false, 406), 2), "html", null, true);
            yield "</span>
                                    </div>
                                    
                                    <div class=\"mt-4\">
                                        ";
            // line 410
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 410, $this->source); })()), "user", [], "any", false, false, false, 410)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 411
                yield "                                            <button class=\"checkout-btn\" onclick=\"alert('Checkout functionality will be implemented in the next step')\">
                                                <i class=\"fas fa-credit-card me-2\"></i>Proceed to Checkout
                                            </button>
                                        ";
            } else {
                // line 415
                yield "                                            <p class=\"text-muted text-center mb-3\">Please log in to checkout</p>
                                            <a href=\"";
                // line 416
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
                yield "\" class=\"checkout-btn text-decoration-none text-center d-block\">
                                                <i class=\"fas fa-sign-in-alt me-2\"></i>Login to Checkout
                                            </a>
                                        ";
            }
            // line 420
            yield "                                    </div>
                                    
                                    <div class=\"mt-3 text-center\">
                                        <small class=\"text-muted\">
                                            <i class=\"fas fa-shield-alt me-1\"></i>
                                            Secure checkout with PayPal
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ";
        } else {
            // line 432
            yield "                        <div class=\"empty-cart\">
                            <i class=\"fas fa-shopping-cart\"></i>
                            <h3>Your cart is empty</h3>
                            <p class=\"mb-4\">Looks like you haven't added any items to your cart yet.</p>
                            <a href=\"";
            // line 436
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos_list");
            yield "\" class=\"continue-shopping\">
                                <i class=\"fas fa-video me-2\"></i>Browse Videos
                            </a>
                        </div>
                    ";
        }
        // line 441
        yield "                </div>
            </div>
        </div>
    </section>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 447
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 448
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        function updateQuantity(type, id, quantity) {
            quantity = parseInt(quantity);
            if (quantity < 0) return;

            fetch('";
        // line 454
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart_update");
        yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `type=\${type}&id=\${id}&quantity=\${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Failed to update cart');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the cart');
            });
        }

        function removeItem(type, id) {
            if (!confirm('Are you sure you want to remove this item from your cart?')) {
                return;
            }

            fetch('";
        // line 480
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart_remove");
        yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `type=\${type}&id=\${id}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Failed to remove item');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while removing the item');
            });
        }

        function clearCart() {
            if (!confirm('Are you sure you want to clear your entire cart?')) {
                return;
            }

            fetch('";
        // line 506
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart_clear");
        yield "', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to clear cart');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while clearing the cart');
            });
        }
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "cart/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  759 => 506,  730 => 480,  701 => 454,  691 => 448,  678 => 447,  663 => 441,  655 => 436,  649 => 432,  635 => 420,  628 => 416,  625 => 415,  619 => 411,  617 => 410,  610 => 406,  602 => 401,  594 => 396,  590 => 395,  572 => 380,  567 => 377,  552 => 370,  538 => 363,  532 => 362,  527 => 360,  515 => 355,  506 => 349,  497 => 343,  493 => 342,  486 => 337,  480 => 333,  475 => 331,  470 => 330,  468 => 329,  460 => 325,  456 => 324,  451 => 321,  449 => 320,  446 => 319,  439 => 315,  434 => 312,  432 => 311,  419 => 300,  406 => 299,  102 => 6,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Shopping Cart - Capitol Academy{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .cart-section {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }

        .cart-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 40px rgba(1, 26, 45, 0.1);
            overflow: hidden;
            border: 1px solid rgba(1, 26, 45, 0.05);
        }

        .cart-header {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }

        .cart-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/></svg>') repeat;
            opacity: 0.3;
        }

        .cart-header h1 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .cart-header p {
            font-family: 'Calibri', Arial, sans-serif;
            opacity: 0.9;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .cart-content {
            padding: 50px 40px;
        }

        .cart-item {
            border: 2px solid #f1f3f4;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .cart-item:hover {
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.1);
            border-color: #e9ecef;
            background: white;
            transform: translateY(-2px);
        }

        .item-thumbnail {
            width: 80px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
        }

        .item-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .item-info h6 {
            color: #011a2d;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .item-type {
            background: #a90418;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            text-transform: uppercase;
        }

        .item-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #a90418;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            background: #011a2d;
            color: white;
            border-color: #011a2d;
        }

        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 5px;
        }

        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 0.85rem;
            transition: background 0.3s ease;
        }

        .remove-btn:hover {
            background: #c82333;
        }

        .cart-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 35px;
            position: sticky;
            top: 100px;
            box-shadow: 0 10px 30px rgba(1, 26, 45, 0.08);
        }

        .summary-title {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 25px;
            font-size: 1.3rem;
            text-align: center;
        }

        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            font-family: 'Calibri', Arial, sans-serif;
        }

        .summary-row.total {
            border-top: 3px solid #011a2d;
            padding-top: 20px;
            margin-top: 20px;
            font-size: 1.3rem;
            font-weight: 700;
            color: #011a2d;
            font-family: 'Montserrat', sans-serif;
        }

        .checkout-btn {
            background: linear-gradient(135deg, #a90418 0%, #8b0314 100%);
            color: white;
            border: none;
            padding: 18px 30px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            font-family: 'Montserrat', sans-serif;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 20px;
        }

        .checkout-btn:hover {
            background: linear-gradient(135deg, #8b0314 0%, #a90418 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(169, 4, 24, 0.3);
        }

        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .empty-cart {
            text-align: center;
            padding: 80px 20px;
            color: #6c757d;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            margin: 20px;
        }

        .empty-cart i {
            font-size: 5rem;
            margin-bottom: 25px;
            color: #011a2d;
            opacity: 0.3;
        }

        .empty-cart h3 {
            font-family: 'Montserrat', sans-serif;
            color: #011a2d;
            margin-bottom: 15px;
        }

        .empty-cart p {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .continue-shopping {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .continue-shopping:hover {
            background: #0d2a42;
            color: white;
            text-decoration: none;
        }

        .alert-removed-items {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .cart-content {
                padding: 20px;
            }
            
            .cart-item {
                padding: 15px;
            }
            
            .item-thumbnail {
                width: 60px;
                height: 45px;
            }
            
            .quantity-controls {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
{% endblock %}

{% block body %}
    <section class=\"cart-section\">
        <div class=\"container\">
            <div class=\"cart-container\">
                <div class=\"cart-header\">
                    <h1 class=\"mb-3\">
                        <i class=\"fas fa-shopping-cart me-3\"></i>Shopping Cart
                    </h1>
                    <p class=\"mb-0\">Review your selected items before checkout</p>
                </div>

                <div class=\"cart-content\">
                    {% if removed_items|length > 0 %}
                        <div class=\"alert-removed-items\">
                            <h6><i class=\"fas fa-exclamation-triangle me-2\"></i>Items Removed</h6>
                            <p class=\"mb-0\">
                                {{ removed_items|length }} item(s) were removed from your cart because they are no longer available.
                            </p>
                        </div>
                    {% endif %}

                    {% if cart.items|length > 0 %}
                        <div class=\"row\">
                            <div class=\"col-lg-8\">
                                <div class=\"cart-items\">
                                    {% for item_key, item in cart.items %}
                                        <div class=\"cart-item\" data-item-key=\"{{ item_key }}\">
                                            <div class=\"row align-items-center\">
                                                <div class=\"col-md-2\">
                                                    <div class=\"item-thumbnail\">
                                                        {% if item.thumbnail %}
                                                            <img src=\"{{ asset('uploads/' ~ item.type ~ 's/thumbnails/' ~ item.thumbnail) }}\" 
                                                                 alt=\"{{ item.title }}\">
                                                        {% else %}
                                                            <div class=\"d-flex align-items-center justify-content-center h-100\">
                                                                <i class=\"fas fa-play text-white\"></i>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                
                                                <div class=\"col-md-4\">
                                                    <div class=\"item-info\">
                                                        <h6>{{ item.title }}</h6>
                                                        <span class=\"item-type\">{{ item.type|replace({'_': ' '})|title }}</span>
                                                    </div>
                                                </div>
                                                
                                                <div class=\"col-md-2\">
                                                    <div class=\"item-price\">
                                                        \${{ item.price|number_format(2) }}
                                                    </div>
                                                </div>
                                                
                                                <div class=\"col-md-2\">
                                                    <div class=\"quantity-controls\">
                                                        <button class=\"quantity-btn\" onclick=\"updateQuantity('{{ item.type }}', {{ item.id }}, {{ item.quantity - 1 }})\">
                                                            <i class=\"fas fa-minus\"></i>
                                                        </button>
                                                        <input type=\"number\" 
                                                               class=\"quantity-input\" 
                                                               value=\"{{ item.quantity }}\" 
                                                               min=\"1\" 
                                                               onchange=\"updateQuantity('{{ item.type }}', {{ item.id }}, this.value)\">
                                                        <button class=\"quantity-btn\" onclick=\"updateQuantity('{{ item.type }}', {{ item.id }}, {{ item.quantity + 1 }})\">
                                                            <i class=\"fas fa-plus\"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <div class=\"col-md-2 text-end\">
                                                    <button class=\"remove-btn\" onclick=\"removeItem('{{ item.type }}', {{ item.id }})\">
                                                        <i class=\"fas fa-trash me-1\"></i>Remove
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>

                                <div class=\"mt-4\">
                                    <a href=\"{{ path('app_videos_list') }}\" class=\"continue-shopping\">
                                        <i class=\"fas fa-arrow-left me-2\"></i>Continue Shopping
                                    </a>
                                    
                                    <button class=\"btn btn-outline-danger ms-3\" onclick=\"clearCart()\">
                                        <i class=\"fas fa-trash me-2\"></i>Clear Cart
                                    </button>
                                </div>
                            </div>

                            <div class=\"col-lg-4\">
                                <div class=\"cart-summary\">
                                    <h5 class=\"summary-title\">Order Summary</h5>
                                    
                                    <div class=\"summary-row\">
                                        <span>Items ({{ cart.total_items }})</span>
                                        <span>\${{ cart.subtotal|number_format(2) }}</span>
                                    </div>
                                    
                                    <div class=\"summary-row\">
                                        <span>Tax</span>
                                        <span>\${{ cart.tax|number_format(2) }}</span>
                                    </div>
                                    
                                    <div class=\"summary-row total\">
                                        <span>Total</span>
                                        <span>\${{ cart.total|number_format(2) }}</span>
                                    </div>
                                    
                                    <div class=\"mt-4\">
                                        {% if app.user %}
                                            <button class=\"checkout-btn\" onclick=\"alert('Checkout functionality will be implemented in the next step')\">
                                                <i class=\"fas fa-credit-card me-2\"></i>Proceed to Checkout
                                            </button>
                                        {% else %}
                                            <p class=\"text-muted text-center mb-3\">Please log in to checkout</p>
                                            <a href=\"{{ path('app_login') }}\" class=\"checkout-btn text-decoration-none text-center d-block\">
                                                <i class=\"fas fa-sign-in-alt me-2\"></i>Login to Checkout
                                            </a>
                                        {% endif %}
                                    </div>
                                    
                                    <div class=\"mt-3 text-center\">
                                        <small class=\"text-muted\">
                                            <i class=\"fas fa-shield-alt me-1\"></i>
                                            Secure checkout with PayPal
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class=\"empty-cart\">
                            <i class=\"fas fa-shopping-cart\"></i>
                            <h3>Your cart is empty</h3>
                            <p class=\"mb-4\">Looks like you haven't added any items to your cart yet.</p>
                            <a href=\"{{ path('app_videos_list') }}\" class=\"continue-shopping\">
                                <i class=\"fas fa-video me-2\"></i>Browse Videos
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        function updateQuantity(type, id, quantity) {
            quantity = parseInt(quantity);
            if (quantity < 0) return;

            fetch('{{ path('app_cart_update') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `type=\${type}&id=\${id}&quantity=\${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Failed to update cart');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the cart');
            });
        }

        function removeItem(type, id) {
            if (!confirm('Are you sure you want to remove this item from your cart?')) {
                return;
            }

            fetch('{{ path('app_cart_remove') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `type=\${type}&id=\${id}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Failed to remove item');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while removing the item');
            });
        }

        function clearCart() {
            if (!confirm('Are you sure you want to clear your entire cart?')) {
                return;
            }

            fetch('{{ path('app_cart_clear') }}', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to clear cart');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while clearing the cart');
            });
        }
    </script>
{% endblock %}
", "cart/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\cart\\index.html.twig");
    }
}
