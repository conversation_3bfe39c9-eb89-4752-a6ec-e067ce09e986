<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getEmailUniquenessValidatorService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Service\EmailUniquenessValidator' shared autowired service.
     *
     * @return \App\Service\EmailUniquenessValidator
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'EmailUniquenessValidator.php';

        return $container->privates['App\\Service\\EmailUniquenessValidator'] = new \App\Service\EmailUniquenessValidator(($container->privates['App\\Repository\\UserRepository'] ?? $container->load('getUserRepositoryService')), ($container->privates['App\\Repository\\AdminRepository'] ?? $container->load('getAdminRepositoryService')));
    }
}
