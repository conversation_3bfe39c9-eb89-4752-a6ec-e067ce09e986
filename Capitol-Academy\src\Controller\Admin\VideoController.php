<?php

namespace App\Controller\Admin;

use App\Entity\Video;
use App\Repository\VideoRepository;
use App\Service\VideoAnalyticsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/videos')]
#[IsGranted('ROLE_ADMIN')]
class VideoController extends AbstractController
{
    private EntityManagerInterface $entityManager;

    public function __construct(
        EntityManagerInterface $entityManager
    ) {
        $this->entityManager = $entityManager;
    }

    #[Route('', name: 'admin_video_index', methods: ['GET'])]
    public function index(VideoRepository $videoRepository, Request $request): Response
    {
        $search = $request->query->get('search');
        $category = $request->query->get('category');
        $status = $request->query->get('status');

        $queryBuilder = $videoRepository->createQueryBuilder('v')
            ->orderBy('v.createdAt', 'DESC');

        if ($search) {
            $queryBuilder->andWhere('v.title LIKE :search OR v.description LIKE :search')
                ->setParameter('search', '%' . $search . '%');
        }

        if ($category) {
            $queryBuilder->andWhere('v.category = :category')
                ->setParameter('category', $category);
        }

        if ($status !== null) {
            $queryBuilder->andWhere('v.isActive = :status')
                ->setParameter('status', $status === 'active');
        }

        $videos = $queryBuilder->getQuery()->getResult();
        $categories = $videoRepository->getCategories();

        return $this->render('admin/video/index.html.twig', [
            'videos' => $videos,
            'categories' => $categories,
            'current_search' => $search,
            'current_category' => $category,
            'current_status' => $status,
        ]);
    }

    #[Route('/new', name: 'admin_video_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        if ($request->isMethod('POST')) {
            // Validate CSRF token
            if (!$this->isCsrfTokenValid('video_create', $request->request->get('_token'))) {
                $this->addFlash('error', 'Invalid CSRF token.');
                return $this->redirectToRoute('admin_video_new');
            }

            try {
                $video = new Video();

                // Set basic fields
                $video->setTitle($request->request->get('title'));
                $video->setCategory($request->request->get('category'));
                $video->setDescription($request->request->get('description'));

                // Handle video source type and related fields
                $videoSourceType = $request->request->get('video_source_type', 'upload');
                $video->setVideoSourceType($videoSourceType);

                // Handle YouTube URL
                if ($videoSourceType === 'youtube') {
                    $youtubeUrl = $request->request->get('youtube_url');
                    if ($youtubeUrl) {
                        $video->setYoutubeUrl($youtubeUrl);
                    }
                }

                // Handle VdoCipher Video ID
                if ($videoSourceType === 'vdocipher') {
                    $vdocipherVideoId = $request->request->get('vdocipher_video_id');
                    if ($vdocipherVideoId) {
                        $video->setVdocipherVideoId($vdocipherVideoId);
                    }
                }

                // Handle access level and pricing
                $accessLevel = $request->request->get('access_level', 'public_free');
                $video->setAccessLevel($accessLevel);

                // Set legacy isFree field for backward compatibility
                $isFree = in_array($accessLevel, ['public_free', 'login_required_free']);
                $video->setIsFree($isFree);

                // Handle pricing for premium videos
                if ($accessLevel === 'premium') {
                    $price = $request->request->get('price');
                    $video->setPrice($price ? (float)$price : 0.00);

                    // Handle access duration
                    $accessDuration = $request->request->get('access_duration');
                    if ($accessDuration && is_numeric($accessDuration)) {
                        $video->setAccessDuration((int)$accessDuration);
                    }
                } else {
                    $video->setPrice(0.00);
                    $video->setAccessDuration(null);
                }

                // Handle video file upload (only for upload source type)
                if ($videoSourceType === 'upload') {
                    $videoFile = $request->files->get('video_file');
                    if ($videoFile) {
                        $newFilename = $this->uploadVideoFile($videoFile, 'files');
                        if ($newFilename) {
                            $video->setVideoFile($newFilename);
                        }
                    }
                }

                // Handle thumbnail upload
                $thumbnailFile = $request->files->get('thumbnail_file');
                if ($thumbnailFile) {
                    $newFilename = $this->uploadVideoFile($thumbnailFile, 'thumbnails');
                    if ($newFilename) {
                        $video->setThumbnail($newFilename);
                    }
                }

                // Set video as active by default
                $video->setIsActive(true);

                $this->entityManager->persist($video);
                $this->entityManager->flush();

                return $this->redirectToRoute('admin_video_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error creating video: ' . $e->getMessage());
            }
        }

        // Get categories from database
        $categories = $this->entityManager->getRepository(\App\Entity\Category::class)->findForVideos();

        return $this->render('admin/video/create.html.twig', [
            'categories' => $categories
        ]);
    }

    #[Route('/{id}', name: 'admin_video_show', methods: ['GET'])]
    public function show(Video $video): Response
    {
        return $this->render('admin/video/show.html.twig', [
            'video' => $video,
        ]);
    }

    #[Route('/{id}/edit', name: 'admin_video_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Video $video): Response
    {

        if ($request->isMethod('POST')) {
            // Validate CSRF token
            if (!$this->isCsrfTokenValid('video_edit', $request->request->get('_token'))) {
                $this->addFlash('error', 'Invalid CSRF token.');
                return $this->redirectToRoute('admin_video_edit', ['id' => $video->getId()]);
            }

            try {
                // Set basic fields
                $video->setTitle($request->request->get('title'));
                $video->setCategory($request->request->get('category'));
                $video->setDescription($request->request->get('description'));

                // Handle video source type and related fields
                $videoSourceType = $request->request->get('video_source_type', $video->getVideoSourceType());
                $video->setVideoSourceType($videoSourceType);

                // Handle YouTube URL
                if ($videoSourceType === 'youtube') {
                    $youtubeUrl = $request->request->get('youtube_url');
                    $video->setYoutubeUrl($youtubeUrl);
                } else {
                    $video->setYoutubeUrl(null);
                }

                // Handle VdoCipher Video ID
                if ($videoSourceType === 'vdocipher') {
                    $vdocipherVideoId = $request->request->get('vdocipher_video_id');
                    $video->setVdocipherVideoId($vdocipherVideoId);
                } else {
                    $video->setVdocipherVideoId(null);
                }

                // Handle access level and pricing
                $accessLevel = $request->request->get('access_level', $video->getAccessLevel());
                $video->setAccessLevel($accessLevel);

                // Set legacy isFree field for backward compatibility
                $isFree = in_array($accessLevel, ['public_free', 'login_required_free']);
                $video->setIsFree($isFree);

                // Handle pricing for premium videos
                if ($accessLevel === 'premium') {
                    $price = $request->request->get('price');
                    $video->setPrice($price ? (float)$price : 0.00);

                    // Handle access duration
                    $accessDuration = $request->request->get('access_duration');
                    if ($accessDuration && is_numeric($accessDuration)) {
                        $video->setAccessDuration((int)$accessDuration);
                    }
                } else {
                    $video->setPrice(0.00);
                    $video->setAccessDuration(null);
                }

                // Handle video file upload (only for upload source type)
                if ($videoSourceType === 'upload') {
                    $videoFile = $request->files->get('video_file');
                    if ($videoFile) {
                        // Delete old video file if it exists
                        $oldVideoFile = $video->getVideoFile();
                        if ($oldVideoFile) {
                            $this->deleteOldVideoFile($oldVideoFile, 'files');
                        }

                        $newFilename = $this->uploadVideoFile($videoFile, 'files');
                        if ($newFilename) {
                            $video->setVideoFile($newFilename);
                        }
                    }
                } else {
                    // Clear video file if source type changed from upload
                    if ($video->getVideoFile()) {
                        $this->deleteOldVideoFile($video->getVideoFile(), 'files');
                        $video->setVideoFile(null);
                    }
                }

                // Handle thumbnail upload (optional)
                $thumbnailFile = $request->files->get('thumbnail_file');
                if ($thumbnailFile) {
                    // Delete old thumbnail if it exists
                    $oldThumbnail = $video->getThumbnail();
                    if ($oldThumbnail) {
                        $this->deleteOldVideoFile($oldThumbnail, 'thumbnails');
                    }

                    $newFilename = $this->uploadVideoFile($thumbnailFile, 'thumbnails');
                    if ($newFilename) {
                        $video->setThumbnail($newFilename);
                    }
                }



                $this->entityManager->flush();

                return $this->redirectToRoute('admin_video_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Error updating video: ' . $e->getMessage());
            }
        }

        // Get categories from database
        $categories = $this->entityManager->getRepository(\App\Entity\Category::class)->findForVideos();

        return $this->render('admin/video/edit.html.twig', [
            'video' => $video,
            'categories' => $categories
        ]);
    }

    #[Route('/{id}/delete', name: 'admin_video_delete', methods: ['POST'])]
    public function delete(Request $request, Video $video): Response
    {
        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            try {
                $this->entityManager->remove($video);
                $this->entityManager->flush();

                return $this->json([
                    'success' => true,
                    'message' => 'Video deleted successfully.'
                ]);
            } catch (\Exception $e) {
                return $this->json([
                    'success' => false,
                    'message' => 'An error occurred while deleting the video.'
                ], 500);
            }
        }

        // Handle regular form submissions
        if ($this->isCsrfTokenValid('delete'.$video->getId(), $request->request->get('_token'))) {
            $this->entityManager->remove($video);
            $this->entityManager->flush();

            $this->addFlash('success', 'Video deleted successfully.');
        }

        return $this->redirectToRoute('admin_video_index');
    }

    #[Route('/{id}/toggle-status', name: 'admin_video_toggle_status', methods: ['POST'])]
    public function toggleStatus(Request $request, Video $video): Response
    {
        // Handle AJAX requests
        if ($request->isXmlHttpRequest()) {
            try {
                $video->setIsActive(!$video->isActive());
                $this->entityManager->flush();

                $status = $video->isActive() ? 'activated' : 'deactivated';

                return $this->json([
                    'success' => true,
                    'message' => "Video {$status} successfully.",
                    'newStatus' => $video->isActive()
                ]);
            } catch (\Exception $e) {
                return $this->json([
                    'success' => false,
                    'message' => 'An error occurred while updating the video status.'
                ], 500);
            }
        }

        // Handle regular form submissions
        if ($this->isCsrfTokenValid('toggle'.$video->getId(), $request->request->get('_token'))) {
            $video->setIsActive(!$video->isActive());
            $this->entityManager->flush();

            $status = $video->isActive() ? 'activated' : 'deactivated';
            $this->addFlash('success', "Video {$status} successfully.");
        }

        return $this->redirectToRoute('admin_video_index');
    }





    private function uploadVideoFile($file, string $directory): ?string
    {
        // Enhanced security validation based on directory type
        if ($directory === 'files') {
            // Video file validation
            $allowedMimeTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
            $allowedExtensions = ['mp4', 'avi', 'mov', 'wmv', 'webm'];
            $maxFileSize = 500 * 1024 * 1024; // 500MB
            $minFileSize = 1024; // 1KB minimum
            $fileTypeError = 'Invalid file type. Only MP4, AVI, MOV, WMV, and WebM video files are allowed.';
        } else {
            // Image file validation (for thumbnails)
            $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'];
            $maxFileSize = 5 * 1024 * 1024; // 5MB
            $minFileSize = 1024; // 1KB minimum
            $fileTypeError = 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.';
        }

        // Validate file object
        if (!$file || !$file->isValid()) {
            $this->addFlash('error', 'Invalid file upload.');
            return null;
        }

        // Validate MIME type
        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            $this->addFlash('error', $fileTypeError);
            return null;
        }

        // Validate file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedExtensions)) {
            $this->addFlash('error', 'Invalid file extension.');
            return null;
        }

        // Validate file size
        if ($file->getSize() > $maxFileSize) {
            $maxSizeText = $directory === 'files' ? '500MB' : '5MB';
            $this->addFlash('error', "File size too large. Maximum size is {$maxSizeText}.");
            return null;
        }

        if ($file->getSize() < $minFileSize) {
            $this->addFlash('error', 'File size too small. Minimum size is 1KB.');
            return null;
        }

        // Generate professional filename
        $fileName = $this->generateProfessionalVideoFilename($directory, $extension);

        // Create upload directory with proper permissions
        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/videos/' . $directory;
        if (!is_dir($uploadDir)) {
            if (!mkdir($uploadDir, 0755, true)) {
                $this->addFlash('error', 'Failed to create upload directory.');
                return null;
            }
        }

        try {
            // Move file with error handling
            $file->move($uploadDir, $fileName);

            // Verify file was uploaded successfully
            $uploadedFile = $uploadDir . '/' . $fileName;
            if (!file_exists($uploadedFile)) {
                $this->addFlash('error', 'File upload verification failed.');
                return null;
            }

            return $fileName;
        } catch (\Exception $e) {
            $this->addFlash('error', 'Failed to upload image: ' . $e->getMessage());
            return null;
        }
    }

    private function generateProfessionalVideoFilename(string $directory, string $extension): string
    {
        // Determine the base name based on directory
        $baseName = match($directory) {
            'thumbnails' => 'videothumbnail',
            'files' => 'videofile',
            default => 'video'
        };

        // Find the next available number
        $uploadDir = $this->getParameter('kernel.project_dir') . '/public/uploads/videos/' . $directory;
        $counter = 1;

        do {
            $fileName = $baseName . sprintf('%04d', $counter) . '.' . $extension;
            $filePath = $uploadDir . '/' . $fileName;
            $counter++;
        } while (file_exists($filePath));

        return $fileName;
    }

    private function deleteOldVideoFile(?string $filename, string $directory): void
    {
        if (!$filename) {
            return;
        }

        $fullPath = $this->getParameter('kernel.project_dir') . '/public/uploads/videos/' . $directory . '/' . $filename;

        if (file_exists($fullPath)) {
            try {
                unlink($fullPath);
            } catch (\Exception $e) {
                // Log error but don't fail the upload process
                error_log('Failed to delete old video file: ' . $e->getMessage());
            }
        }
    }

    #[Route('/analytics', name: 'admin_video_analytics')]
    public function analytics(VideoAnalyticsService $analyticsService): Response
    {
        $analytics = $analyticsService->getVideoAnalytics();
        $userEngagement = $analyticsService->getUserEngagementMetrics();

        return $this->render('admin/video/analytics.html.twig', [
            'analytics' => $analytics,
            'user_engagement' => $userEngagement,
        ]);
    }

    #[Route('/{id}/performance', name: 'admin_video_performance', requirements: ['id' => '\d+'])]
    public function performance(int $id, VideoAnalyticsService $analyticsService): Response
    {
        $performance = $analyticsService->getVideoPerformance($id);

        if (empty($performance)) {
            throw $this->createNotFoundException('Video not found');
        }

        return $this->render('admin/video/performance.html.twig', [
            'performance' => $performance,
        ]);
    }
}
