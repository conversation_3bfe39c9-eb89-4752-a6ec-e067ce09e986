<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getMarketAnalysisService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored..service_locator.WGDSHn2.App\Entity\MarketAnalysis' shared service.
     *
     * @return \App\Entity\MarketAnalysis
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot autowire service ".service_locator.WGDSHn2": it needs an instance of "App\\Entity\\MarketAnalysis" but this type has been excluded in "config/services.yaml".');
    }
}
