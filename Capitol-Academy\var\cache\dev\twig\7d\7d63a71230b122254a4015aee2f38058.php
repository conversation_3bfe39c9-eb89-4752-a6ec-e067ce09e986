<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* video/show.html.twig */
class __TwigTemplate_ff53fff703f82da28fc34ce1d34c40a2 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "video/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "video/show.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 3, $this->source); })()), "title", [], "any", false, false, false, 3), "html", null, true);
        yield " - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "description", [], "any", false, false, false, 5)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "description", [], "any", false, false, false, 5), 0, 160), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((("Watch " . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "title", [], "any", false, false, false, 5)) . " on Capitol Academy - Professional Trading Education"), "html", null, true)));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        /* Capitol Academy Video Detail Page Styles */
        .video-detail-section {
            background: #f8f9fa;
            padding-top: 100px;
            padding-bottom: 60px;
            min-height: 100vh;
        }

        .video-main-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(1, 26, 45, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .video-player-section {
            background: #000;
            position: relative;
        }

        .video-player-wrapper {
            position: relative;
            width: 100%;
            background: #000;
        }

        .video-info-section {
            padding: 30px;
        }

        .video-player {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-player iframe,
        .video-player video {
            width: 100%;
            height: 100%;
            border: none;
        }

        .video-description {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #343a40;
            margin-bottom: 30px;
        }

        .video-title {
            color: #011a2d;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
            align-items: center;
        }

        .video-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .badge-free {
            background: #28a745;
            color: white;
        }

        .badge-premium {
            background: #a90418;
            color: white;
        }

        .badge-login-required {
            background: #011a2d;
            color: white;
        }

        .video-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .video-description {
            color: #343a40;
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .access-denied-card {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-top: 30px;
        }

        .access-denied-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .purchase-button {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .purchase-button:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(169, 4, 24, 0.3);
            color: white;
            text-decoration: none;
        }

        .login-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
            color: white;
            text-decoration: none;
        }

        .premium-video-info {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
        }

        .price-info {
            margin-bottom: 20px;
        }

        .price-display {
            font-size: 2rem;
            font-weight: 700;
            color: #a90418;
            margin-bottom: 8px;
            font-family: 'Montserrat', sans-serif;
        }

        .access-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .add-to-cart-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
        }

        .add-to-cart-button:hover {
            background: linear-gradient(135deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .add-to-cart-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .related-videos-section {
            background: #f8f9fa;
            padding: 60px 0;
        }

        .related-video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .related-video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .related-video-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .related-video-content {
            padding: 20px;
        }

        .related-video-title {
            color: #011a2d;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .vdocipher-player {
            background: #000;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
            position: relative;
        }

        .vdocipher-loading {
            color: white;
            text-align: center;
        }

        .video-source-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .video-hero-section {
                padding-top: 60px;
                min-height: auto;
            }

            .video-player {
                height: 300px;
            }

            .video-title {
                font-size: 2rem;
            }

            .video-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 325
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 326
        yield "<!-- Video Detail Section -->
<section class=\"video-detail-section\">
    <div class=\"container\">
        <div class=\"row justify-content-center\">
            <div class=\"col-lg-10\">
                <!-- Video Main Container -->
                <div class=\"video-main-container\">
                    <!-- Video Player Section -->
                    <div class=\"video-player-section\">
                        <div class=\"video-player-wrapper\">
                            <div class=\"video-player\">
                            ";
        // line 337
        if ((($tmp = (isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 337, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 338
            yield "                                <!-- Video Source Indicator -->
                                <div class=\"video-source-indicator\">
                                    ";
            // line 340
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 340, $this->source); })()), "videoSourceType", [], "any", false, false, false, 340) == "youtube")) {
                // line 341
                yield "                                        <i class=\"fab fa-youtube\"></i> YouTube
                                    ";
            } elseif ((CoreExtension::getAttribute($this->env, $this->source,             // line 342
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 342, $this->source); })()), "videoSourceType", [], "any", false, false, false, 342) == "vdocipher")) {
                // line 343
                yield "                                        <i class=\"fas fa-shield-alt\"></i> VdoCipher
                                    ";
            } else {
                // line 345
                yield "                                        <i class=\"fas fa-play\"></i> Direct
                                    ";
            }
            // line 347
            yield "                                </div>

                                <!-- YouTube Video Player -->
                                ";
            // line 350
            if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 350, $this->source); })()), "videoSourceType", [], "any", false, false, false, 350) == "youtube") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 350, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 350))) {
                // line 351
                yield "                                    <iframe src=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 351, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 351), "html", null, true);
                yield "\"
                                            frameborder=\"0\"
                                            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                            allowfullscreen>
                                    </iframe>

                                <!-- VdoCipher Video Player -->
                                ";
            } elseif (((CoreExtension::getAttribute($this->env, $this->source,             // line 358
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 358, $this->source); })()), "videoSourceType", [], "any", false, false, false, 358) == "vdocipher") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 358, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 358))) {
                // line 359
                yield "                                    <div class=\"vdocipher-player\" id=\"vdocipher-player\">
                                        <div class=\"vdocipher-loading\">
                                            <i class=\"fas fa-spinner fa-spin\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Loading Secure Video...</h4>
                                            <p>Initializing DRM-protected playback</p>
                                        </div>
                                    </div>

                                <!-- Direct Upload Video Player -->
                                ";
            } elseif ((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 368
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 368, $this->source); })()), "videoFile", [], "any", false, false, false, 368)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 369
                yield "                                    <video controls preload=\"metadata\" poster=\"";
                yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 369, $this->source); })()), "thumbnail", [], "any", false, false, false, 369)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 369, $this->source); })()), "thumbnail", [], "any", false, false, false, 369))), "html", null, true)) : (""));
                yield "\">
                                        <source src=\"";
                // line 370
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/files/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 370, $this->source); })()), "videoFile", [], "any", false, false, false, 370))), "html", null, true);
                yield "\" type=\"video/mp4\">
                                        <p>Your browser doesn't support HTML5 video. <a href=\"";
                // line 371
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/files/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 371, $this->source); })()), "videoFile", [], "any", false, false, false, 371))), "html", null, true);
                yield "\">Download the video</a> instead.</p>
                                    </video>

                                <!-- No Video Source -->
                                ";
            } else {
                // line 376
                yield "                                    <div class=\"d-flex align-items-center justify-content-center h-100\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-exclamation-triangle\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Video Not Available</h4>
                                            <p>This video is currently being processed. Please try again later.</p>
                                        </div>
                                    </div>
                                ";
            }
            // line 384
            yield "                            ";
        } else {
            // line 385
            yield "                                <!-- Access Denied - Show Thumbnail with Overlay -->
                                <div class=\"d-flex align-items-center justify-content-center h-100\" style=\"background: ";
            // line 386
            yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 386, $this->source); })()), "thumbnail", [], "any", false, false, false, 386)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((("url(" . $this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 386, $this->source); })()), "thumbnail", [], "any", false, false, false, 386)))) . ")"), "html", null, true)) : ("#000"));
            yield " center/cover;\">
                                    <div style=\"background: rgba(0, 0, 0, 0.8); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-lock\" style=\"font-size: 4rem; margin-bottom: 1rem;\"></i>
                                            <h3>Premium Content</h3>
                                            <p class=\"mb-0\">This video requires access to view</p>
                                        </div>
                                    </div>
                                </div>
                            ";
        }
        // line 396
        yield "                        </div>
                    </div>

                    </div>

                    <!-- Video Information Section -->
                    <div class=\"video-info-section\">
                        <h1 class=\"video-title\">";
        // line 403
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 403, $this->source); })()), "title", [], "any", false, false, false, 403), "html", null, true);
        yield "</h1>

                        <!-- Video Meta Information -->
                        <div class=\"video-meta\">
                            <!-- Access Level Badge -->
                            ";
        // line 408
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 408, $this->source); })()), "accessLevel", [], "any", false, false, false, 408) == "public_free")) {
            // line 409
            yield "                                <span class=\"video-badge badge-free\">
                                    <i class=\"fas fa-globe\"></i>
                                    Public Free
                                </span>
                            ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 413
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 413, $this->source); })()), "accessLevel", [], "any", false, false, false, 413) == "login_required_free")) {
            // line 414
            yield "                                <span class=\"video-badge badge-login-required\">
                                    <i class=\"fas fa-user\"></i>
                                    Login Required
                                </span>
                            ";
        } else {
            // line 419
            yield "                                <span class=\"video-badge badge-premium\">
                                    <i class=\"fas fa-crown\"></i>
                                    Premium - ";
            // line 421
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 421, $this->source); })()), "formattedPrice", [], "any", false, false, false, 421), "html", null, true);
            yield "
                                    ";
            // line 422
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 422, $this->source); })()), "accessDuration", [], "any", false, false, false, 422)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 423
                yield "                                        (";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 423, $this->source); })()), "formattedAccessDuration", [], "any", false, false, false, 423), "html", null, true);
                yield ")
                                    ";
            }
            // line 425
            yield "                                </span>
                            ";
        }
        // line 427
        yield "
                            <!-- Category Badge -->
                            ";
        // line 429
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 429, $this->source); })()), "category", [], "any", false, false, false, 429)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 430
            yield "                                <span class=\"video-badge\" style=\"background: #6c757d; color: white;\">
                                    <i class=\"fas fa-tag\"></i>
                                    ";
            // line 432
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 432, $this->source); })()), "category", [], "any", false, false, false, 432), "html", null, true);
            yield "
                                </span>
                            ";
        }
        // line 435
        yield "
                            <!-- Video Source Badge -->
                            <span class=\"video-badge\" style=\"background: #17a2b8; color: white;\">
                                ";
        // line 438
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 438, $this->source); })()), "videoSourceType", [], "any", false, false, false, 438) == "youtube")) {
            // line 439
            yield "                                    <i class=\"fab fa-youtube\"></i>
                                    YouTube
                                ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 441
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 441, $this->source); })()), "videoSourceType", [], "any", false, false, false, 441) == "vdocipher")) {
            // line 442
            yield "                                    <i class=\"fas fa-shield-alt\"></i>
                                    VdoCipher
                                ";
        } else {
            // line 445
            yield "                                    <i class=\"fas fa-upload\"></i>
                                    Direct Upload
                                ";
        }
        // line 448
        yield "                            </span>

                            <!-- Upload Date -->
                            <span class=\"video-date\">
                                <i class=\"fas fa-calendar me-1\"></i>
                                ";
        // line 453
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 453, $this->source); })()), "createdAt", [], "any", false, false, false, 453), "M d, Y"), "html", null, true);
        yield "
                            </span>
                        </div>

                        <!-- Video Description -->
                        ";
        // line 458
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 458, $this->source); })()), "description", [], "any", false, false, false, 458)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 459
            yield "                            <div class=\"video-description\">
                                ";
            // line 460
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 460, $this->source); })()), "description", [], "any", false, false, false, 460), "html", null, true));
            yield "
                            </div>
                        ";
        }
        // line 463
        yield "
                        <!-- Access Control Actions -->
                        ";
        // line 465
        if ((($tmp =  !(isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 465, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 466
            yield "                            ";
            if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 466, $this->source); })()), "accessLevel", [], "any", false, false, false, 466) == "login_required_free") &&  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 466, $this->source); })()), "user", [], "any", false, false, false, 466))) {
                // line 467
                yield "                                <!-- Login Required for Free Video -->
                                <div class=\"text-center\">
                                    <p class=\"mb-3\">Please log in to watch this free video.</p>
                                    <a href=\"";
                // line 470
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
                yield "\" class=\"login-button\">
                                        <i class=\"fas fa-sign-in-alt\"></i>
                                        Login to Watch
                                    </a>
                                </div>
                            ";
            } elseif ((CoreExtension::getAttribute($this->env, $this->source,             // line 475
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 475, $this->source); })()), "accessLevel", [], "any", false, false, false, 475) == "premium")) {
                // line 476
                yield "                                <!-- Premium Video Purchase -->
                                <div class=\"text-center\">
                                    <div class=\"premium-video-info\">
                                        <div class=\"price-info mb-3\">
                                            <h4 class=\"price-display\">";
                // line 480
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 480, $this->source); })()), "formattedPrice", [], "any", false, false, false, 480), "html", null, true);
                yield "</h4>
                                            <p class=\"access-info\">
                                                ";
                // line 482
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 482, $this->source); })()), "accessDuration", [], "any", false, false, false, 482)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 483
                    yield "                                                    ";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 483, $this->source); })()), "accessDuration", [], "any", false, false, false, 483), "html", null, true);
                    yield " days access
                                                ";
                } else {
                    // line 485
                    yield "                                                    Lifetime access
                                                ";
                }
                // line 487
                yield "                                            </p>
                                        </div>
                                        <button class=\"add-to-cart-button\" onclick=\"addToCart('video', ";
                // line 489
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 489, $this->source); })()), "id", [], "any", false, false, false, 489), "html", null, true);
                yield ")\">
                                            <i class=\"fas fa-cart-plus\"></i>
                                            Add to Cart
                                        </button>
                                        ";
                // line 493
                if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 493, $this->source); })()), "user", [], "any", false, false, false, 493)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 494
                    yield "                                            <p class=\"mt-3 text-muted small\">
                                                <i class=\"fas fa-info-circle me-1\"></i>
                                                You'll be asked to login during checkout
                                            </p>
                                        ";
                }
                // line 499
                yield "                                    </div>
                                </div>
                            ";
            }
            // line 502
            yield "                        ";
        }
        // line 503
        yield "                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Videos Section -->
";
        // line 511
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["related_videos"]) || array_key_exists("related_videos", $context) ? $context["related_videos"] : (function () { throw new RuntimeError('Variable "related_videos" does not exist.', 511, $this->source); })())) > 0)) {
            // line 512
            yield "<section class=\"related-videos-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-12\">
                <h2 class=\"text-center mb-5\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 700;\">
                    Related Videos
                </h2>
            </div>
        </div>
        <div class=\"row g-4\">
            ";
            // line 522
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["related_videos"]) || array_key_exists("related_videos", $context) ? $context["related_videos"] : (function () { throw new RuntimeError('Variable "related_videos" does not exist.', 522, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["related_video"]) {
                // line 523
                yield "                <div class=\"col-lg-3 col-md-6\">
                    <div class=\"related-video-card\">
                        <a href=\"";
                // line 525
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_show", ["slug" => CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "slug", [], "any", false, false, false, 525)]), "html", null, true);
                yield "\" class=\"text-decoration-none\">
                            ";
                // line 526
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "thumbnail", [], "any", false, false, false, 526)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 527
                    yield "                                <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "thumbnail", [], "any", false, false, false, 527))), "html", null, true);
                    yield "\"
                                     alt=\"";
                    // line 528
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "title", [], "any", false, false, false, 528), "html", null, true);
                    yield "\"
                                     class=\"related-video-thumbnail\">
                            ";
                } else {
                    // line 531
                    yield "                                <div class=\"related-video-thumbnail d-flex align-items-center justify-content-center\" style=\"background: #f8f9fa;\">
                                    <i class=\"fas fa-video text-muted\" style=\"font-size: 3rem;\"></i>
                                </div>
                            ";
                }
                // line 535
                yield "                            <div class=\"related-video-content\">
                                <h5 class=\"related-video-title\">";
                // line 536
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "title", [], "any", false, false, false, 536), "html", null, true);
                yield "</h5>
                                <div class=\"d-flex justify-content-between align-items-center\">
                                    <small class=\"text-muted\">
                                        <i class=\"fas fa-calendar me-1\"></i>
                                        ";
                // line 540
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "createdAt", [], "any", false, false, false, 540), "M d, Y"), "html", null, true);
                yield "
                                    </small>
                                    ";
                // line 542
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "accessLevel", [], "any", false, false, false, 542) == "public_free")) {
                    // line 543
                    yield "                                        <span class=\"badge bg-success\">Free</span>
                                    ";
                } elseif ((CoreExtension::getAttribute($this->env, $this->source,                 // line 544
$context["related_video"], "accessLevel", [], "any", false, false, false, 544) == "login_required_free")) {
                    // line 545
                    yield "                                        <span class=\"badge bg-info\">Login Required</span>
                                    ";
                } else {
                    // line 547
                    yield "                                        <span class=\"badge bg-warning text-dark\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "formattedPrice", [], "any", false, false, false, 547), "html", null, true);
                    yield "</span>
                                    ";
                }
                // line 549
                yield "                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['related_video'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 555
            yield "        </div>
    </div>
</section>
";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 561
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 562
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        // VdoCipher Player Initialization
        ";
        // line 565
        if ((((isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 565, $this->source); })()) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 565, $this->source); })()), "videoSourceType", [], "any", false, false, false, 565) == "vdocipher")) && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 565, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 565))) {
            // line 566
            yield "        document.addEventListener('DOMContentLoaded', function() {
            initializeVdoCipherPlayer();
        });

        function initializeVdoCipherPlayer() {
            // Get video access token from server
            fetch('";
            // line 572
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_vdocipher_token", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 572, $this->source); })()), "id", [], "any", false, false, false, 572)]), "html", null, true);
            yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.otp && data.playbackInfo) {
                    // Initialize VdoCipher player
                    new VdoPlayer({
                        otp: data.otp,
                        playbackInfo: data.playbackInfo,
                        theme: \"9bb535\",
                        container: document.querySelector(\"#vdocipher-player\"),
                        width: \"100%\",
                        height: \"100%\"
                    });
                } else {
                    showVdoCipherError('Failed to load video. Please try again.');
                }
            })
            .catch(error => {
                console.error('VdoCipher initialization error:', error);
                showVdoCipherError('Unable to load video player. Please refresh the page.');
            });
        }

        function showVdoCipherError(message) {
            const player = document.getElementById('vdocipher-player');
            player.innerHTML = `
                <div class=\"vdocipher-loading\">
                    <i class=\"fas fa-exclamation-triangle text-warning\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                    <h4>Video Loading Error</h4>
                    <p>\${message}</p>
                    <button class=\"btn btn-primary mt-3\" onclick=\"initializeVdoCipherPlayer()\">
                        <i class=\"fas fa-redo\"></i> Retry
                    </button>
                </div>
            `;
        }
        ";
        }
        // line 615
        yield "
        // Video Purchase Function
        function purchaseVideo(videoId) {
            // Show loading state
            const button = event.target.closest('.purchase-button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Processing...';
            button.disabled = true;

            // Create Stripe checkout session
            fetch('";
        // line 625
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_purchase");
        yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    video_id: videoId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.checkout_url) {
                    // Redirect to Stripe checkout
                    window.location.href = data.checkout_url;
                } else {
                    // Show error message
                    alert(data.message || 'Unable to process purchase. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Purchase error:', error);
                alert('Unable to process purchase. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }

        // Video Analytics Tracking
        ";
        // line 656
        if ((($tmp = (isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 656, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 657
            yield "        document.addEventListener('DOMContentLoaded', function() {
            // Track video view
            fetch('";
            // line 659
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_track_view", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 659, $this->source); })()), "slug", [], "any", false, false, false, 659)]), "html", null, true);
            yield "', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            // Track video engagement
            let watchTime = 0;
            const trackingInterval = setInterval(() => {
                watchTime += 5; // Track every 5 seconds

                // Send tracking data every 30 seconds
                if (watchTime % 30 === 0) {
                    fetch('";
            // line 673
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_track_engagement", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 673, $this->source); })()), "slug", [], "any", false, false, false, 673)]), "html", null, true);
            yield "', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            watch_time: watchTime
                        })
                    });
                }
            }, 5000);

            // Stop tracking when user leaves page
            window.addEventListener('beforeunload', () => {
                clearInterval(trackingInterval);
            });
        });
        ";
        }
        // line 692
        yield "
        // Smooth scroll for related videos
        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add to Cart Function
        function addToCart(type, id) {
            const button = event.target.closest('.add-to-cart-button');
            const originalContent = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Adding...';
            button.disabled = true;

            fetch('";
        // line 716
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart_add");
        yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `type=\${type}&id=\${id}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    button.innerHTML = '<i class=\"fas fa-check\"></i> Added to Cart!';
                    button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

                    // Update cart display
                    if (typeof updateCartBadge === 'function') {
                        updateCartBadge(data.cart.item_count);
                    }
                    if (typeof loadCartContents === 'function') {
                        loadCartContents();
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                        button.style.background = '';
                    }, 2000);
                } else {
                    // Show error message
                    alert(data.message || 'Unable to add item to cart. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Add to cart error:', error);
                alert('Unable to add item to cart. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }
    </script>

    <!-- VdoCipher Player Script -->
    ";
        // line 762
        if (((isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 762, $this->source); })()) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 762, $this->source); })()), "videoSourceType", [], "any", false, false, false, 762) == "vdocipher"))) {
            // line 763
            yield "    <script src=\"https://player.vdocipher.com/v2/api.js\"></script>
    ";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "video/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1132 => 763,  1130 => 762,  1081 => 716,  1055 => 692,  1033 => 673,  1016 => 659,  1012 => 657,  1010 => 656,  976 => 625,  964 => 615,  918 => 572,  910 => 566,  908 => 565,  901 => 562,  888 => 561,  873 => 555,  862 => 549,  856 => 547,  852 => 545,  850 => 544,  847 => 543,  845 => 542,  840 => 540,  833 => 536,  830 => 535,  824 => 531,  818 => 528,  813 => 527,  811 => 526,  807 => 525,  803 => 523,  799 => 522,  787 => 512,  785 => 511,  775 => 503,  772 => 502,  767 => 499,  760 => 494,  758 => 493,  751 => 489,  747 => 487,  743 => 485,  737 => 483,  735 => 482,  730 => 480,  724 => 476,  722 => 475,  714 => 470,  709 => 467,  706 => 466,  704 => 465,  700 => 463,  694 => 460,  691 => 459,  689 => 458,  681 => 453,  674 => 448,  669 => 445,  664 => 442,  662 => 441,  658 => 439,  656 => 438,  651 => 435,  645 => 432,  641 => 430,  639 => 429,  635 => 427,  631 => 425,  625 => 423,  623 => 422,  619 => 421,  615 => 419,  608 => 414,  606 => 413,  600 => 409,  598 => 408,  590 => 403,  581 => 396,  568 => 386,  565 => 385,  562 => 384,  552 => 376,  544 => 371,  540 => 370,  535 => 369,  533 => 368,  522 => 359,  520 => 358,  509 => 351,  507 => 350,  502 => 347,  498 => 345,  494 => 343,  492 => 342,  489 => 341,  487 => 340,  483 => 338,  481 => 337,  468 => 326,  455 => 325,  127 => 8,  114 => 7,  91 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}{{ video.title }} - Capitol Academy{% endblock %}

{% block meta_description %}{{ video.description ? video.description|slice(0, 160) : 'Watch ' ~ video.title ~ ' on Capitol Academy - Professional Trading Education' }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Capitol Academy Video Detail Page Styles */
        .video-detail-section {
            background: #f8f9fa;
            padding-top: 100px;
            padding-bottom: 60px;
            min-height: 100vh;
        }

        .video-main-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(1, 26, 45, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .video-player-section {
            background: #000;
            position: relative;
        }

        .video-player-wrapper {
            position: relative;
            width: 100%;
            background: #000;
        }

        .video-info-section {
            padding: 30px;
        }

        .video-player {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-player iframe,
        .video-player video {
            width: 100%;
            height: 100%;
            border: none;
        }

        .video-description {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #343a40;
            margin-bottom: 30px;
        }

        .video-title {
            color: #011a2d;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
            align-items: center;
        }

        .video-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .badge-free {
            background: #28a745;
            color: white;
        }

        .badge-premium {
            background: #a90418;
            color: white;
        }

        .badge-login-required {
            background: #011a2d;
            color: white;
        }

        .video-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .video-description {
            color: #343a40;
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .access-denied-card {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-top: 30px;
        }

        .access-denied-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .purchase-button {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .purchase-button:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(169, 4, 24, 0.3);
            color: white;
            text-decoration: none;
        }

        .login-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
            color: white;
            text-decoration: none;
        }

        .premium-video-info {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
        }

        .price-info {
            margin-bottom: 20px;
        }

        .price-display {
            font-size: 2rem;
            font-weight: 700;
            color: #a90418;
            margin-bottom: 8px;
            font-family: 'Montserrat', sans-serif;
        }

        .access-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .add-to-cart-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
        }

        .add-to-cart-button:hover {
            background: linear-gradient(135deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .add-to-cart-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .related-videos-section {
            background: #f8f9fa;
            padding: 60px 0;
        }

        .related-video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .related-video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .related-video-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .related-video-content {
            padding: 20px;
        }

        .related-video-title {
            color: #011a2d;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .vdocipher-player {
            background: #000;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
            position: relative;
        }

        .vdocipher-loading {
            color: white;
            text-align: center;
        }

        .video-source-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .video-hero-section {
                padding-top: 60px;
                min-height: auto;
            }

            .video-player {
                height: 300px;
            }

            .video-title {
                font-size: 2rem;
            }

            .video-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
{% endblock %}

{% block body %}
<!-- Video Detail Section -->
<section class=\"video-detail-section\">
    <div class=\"container\">
        <div class=\"row justify-content-center\">
            <div class=\"col-lg-10\">
                <!-- Video Main Container -->
                <div class=\"video-main-container\">
                    <!-- Video Player Section -->
                    <div class=\"video-player-section\">
                        <div class=\"video-player-wrapper\">
                            <div class=\"video-player\">
                            {% if has_access %}
                                <!-- Video Source Indicator -->
                                <div class=\"video-source-indicator\">
                                    {% if video.videoSourceType == 'youtube' %}
                                        <i class=\"fab fa-youtube\"></i> YouTube
                                    {% elseif video.videoSourceType == 'vdocipher' %}
                                        <i class=\"fas fa-shield-alt\"></i> VdoCipher
                                    {% else %}
                                        <i class=\"fas fa-play\"></i> Direct
                                    {% endif %}
                                </div>

                                <!-- YouTube Video Player -->
                                {% if video.videoSourceType == 'youtube' and video.youtubeEmbedUrl %}
                                    <iframe src=\"{{ video.youtubeEmbedUrl }}\"
                                            frameborder=\"0\"
                                            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                            allowfullscreen>
                                    </iframe>

                                <!-- VdoCipher Video Player -->
                                {% elseif video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                                    <div class=\"vdocipher-player\" id=\"vdocipher-player\">
                                        <div class=\"vdocipher-loading\">
                                            <i class=\"fas fa-spinner fa-spin\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Loading Secure Video...</h4>
                                            <p>Initializing DRM-protected playback</p>
                                        </div>
                                    </div>

                                <!-- Direct Upload Video Player -->
                                {% elseif video.videoFile %}
                                    <video controls preload=\"metadata\" poster=\"{{ video.thumbnail ? asset('uploads/videos/thumbnails/' ~ video.thumbnail) : '' }}\">
                                        <source src=\"{{ asset('uploads/videos/files/' ~ video.videoFile) }}\" type=\"video/mp4\">
                                        <p>Your browser doesn't support HTML5 video. <a href=\"{{ asset('uploads/videos/files/' ~ video.videoFile) }}\">Download the video</a> instead.</p>
                                    </video>

                                <!-- No Video Source -->
                                {% else %}
                                    <div class=\"d-flex align-items-center justify-content-center h-100\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-exclamation-triangle\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Video Not Available</h4>
                                            <p>This video is currently being processed. Please try again later.</p>
                                        </div>
                                    </div>
                                {% endif %}
                            {% else %}
                                <!-- Access Denied - Show Thumbnail with Overlay -->
                                <div class=\"d-flex align-items-center justify-content-center h-100\" style=\"background: {{ video.thumbnail ? 'url(' ~ asset('uploads/videos/thumbnails/' ~ video.thumbnail) ~ ')' : '#000' }} center/cover;\">
                                    <div style=\"background: rgba(0, 0, 0, 0.8); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-lock\" style=\"font-size: 4rem; margin-bottom: 1rem;\"></i>
                                            <h3>Premium Content</h3>
                                            <p class=\"mb-0\">This video requires access to view</p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    </div>

                    <!-- Video Information Section -->
                    <div class=\"video-info-section\">
                        <h1 class=\"video-title\">{{ video.title }}</h1>

                        <!-- Video Meta Information -->
                        <div class=\"video-meta\">
                            <!-- Access Level Badge -->
                            {% if video.accessLevel == 'public_free' %}
                                <span class=\"video-badge badge-free\">
                                    <i class=\"fas fa-globe\"></i>
                                    Public Free
                                </span>
                            {% elseif video.accessLevel == 'login_required_free' %}
                                <span class=\"video-badge badge-login-required\">
                                    <i class=\"fas fa-user\"></i>
                                    Login Required
                                </span>
                            {% else %}
                                <span class=\"video-badge badge-premium\">
                                    <i class=\"fas fa-crown\"></i>
                                    Premium - {{ video.formattedPrice }}
                                    {% if video.accessDuration %}
                                        ({{ video.formattedAccessDuration }})
                                    {% endif %}
                                </span>
                            {% endif %}

                            <!-- Category Badge -->
                            {% if video.category %}
                                <span class=\"video-badge\" style=\"background: #6c757d; color: white;\">
                                    <i class=\"fas fa-tag\"></i>
                                    {{ video.category }}
                                </span>
                            {% endif %}

                            <!-- Video Source Badge -->
                            <span class=\"video-badge\" style=\"background: #17a2b8; color: white;\">
                                {% if video.videoSourceType == 'youtube' %}
                                    <i class=\"fab fa-youtube\"></i>
                                    YouTube
                                {% elseif video.videoSourceType == 'vdocipher' %}
                                    <i class=\"fas fa-shield-alt\"></i>
                                    VdoCipher
                                {% else %}
                                    <i class=\"fas fa-upload\"></i>
                                    Direct Upload
                                {% endif %}
                            </span>

                            <!-- Upload Date -->
                            <span class=\"video-date\">
                                <i class=\"fas fa-calendar me-1\"></i>
                                {{ video.createdAt|date('M d, Y') }}
                            </span>
                        </div>

                        <!-- Video Description -->
                        {% if video.description %}
                            <div class=\"video-description\">
                                {{ video.description|nl2br }}
                            </div>
                        {% endif %}

                        <!-- Access Control Actions -->
                        {% if not has_access %}
                            {% if video.accessLevel == 'login_required_free' and not app.user %}
                                <!-- Login Required for Free Video -->
                                <div class=\"text-center\">
                                    <p class=\"mb-3\">Please log in to watch this free video.</p>
                                    <a href=\"{{ path('app_login') }}\" class=\"login-button\">
                                        <i class=\"fas fa-sign-in-alt\"></i>
                                        Login to Watch
                                    </a>
                                </div>
                            {% elseif video.accessLevel == 'premium' %}
                                <!-- Premium Video Purchase -->
                                <div class=\"text-center\">
                                    <div class=\"premium-video-info\">
                                        <div class=\"price-info mb-3\">
                                            <h4 class=\"price-display\">{{ video.formattedPrice }}</h4>
                                            <p class=\"access-info\">
                                                {% if video.accessDuration %}
                                                    {{ video.accessDuration }} days access
                                                {% else %}
                                                    Lifetime access
                                                {% endif %}
                                            </p>
                                        </div>
                                        <button class=\"add-to-cart-button\" onclick=\"addToCart('video', {{ video.id }})\">
                                            <i class=\"fas fa-cart-plus\"></i>
                                            Add to Cart
                                        </button>
                                        {% if not app.user %}
                                            <p class=\"mt-3 text-muted small\">
                                                <i class=\"fas fa-info-circle me-1\"></i>
                                                You'll be asked to login during checkout
                                            </p>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Videos Section -->
{% if related_videos|length > 0 %}
<section class=\"related-videos-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-12\">
                <h2 class=\"text-center mb-5\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 700;\">
                    Related Videos
                </h2>
            </div>
        </div>
        <div class=\"row g-4\">
            {% for related_video in related_videos %}
                <div class=\"col-lg-3 col-md-6\">
                    <div class=\"related-video-card\">
                        <a href=\"{{ path('app_video_show', {'slug': related_video.slug}) }}\" class=\"text-decoration-none\">
                            {% if related_video.thumbnail %}
                                <img src=\"{{ asset('uploads/videos/thumbnails/' ~ related_video.thumbnail) }}\"
                                     alt=\"{{ related_video.title }}\"
                                     class=\"related-video-thumbnail\">
                            {% else %}
                                <div class=\"related-video-thumbnail d-flex align-items-center justify-content-center\" style=\"background: #f8f9fa;\">
                                    <i class=\"fas fa-video text-muted\" style=\"font-size: 3rem;\"></i>
                                </div>
                            {% endif %}
                            <div class=\"related-video-content\">
                                <h5 class=\"related-video-title\">{{ related_video.title }}</h5>
                                <div class=\"d-flex justify-content-between align-items-center\">
                                    <small class=\"text-muted\">
                                        <i class=\"fas fa-calendar me-1\"></i>
                                        {{ related_video.createdAt|date('M d, Y') }}
                                    </small>
                                    {% if related_video.accessLevel == 'public_free' %}
                                        <span class=\"badge bg-success\">Free</span>
                                    {% elseif related_video.accessLevel == 'login_required_free' %}
                                        <span class=\"badge bg-info\">Login Required</span>
                                    {% else %}
                                        <span class=\"badge bg-warning text-dark\">{{ related_video.formattedPrice }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // VdoCipher Player Initialization
        {% if has_access and video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
        document.addEventListener('DOMContentLoaded', function() {
            initializeVdoCipherPlayer();
        });

        function initializeVdoCipherPlayer() {
            // Get video access token from server
            fetch('{{ path('app_video_vdocipher_token', {'id': video.id}) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.otp && data.playbackInfo) {
                    // Initialize VdoCipher player
                    new VdoPlayer({
                        otp: data.otp,
                        playbackInfo: data.playbackInfo,
                        theme: \"9bb535\",
                        container: document.querySelector(\"#vdocipher-player\"),
                        width: \"100%\",
                        height: \"100%\"
                    });
                } else {
                    showVdoCipherError('Failed to load video. Please try again.');
                }
            })
            .catch(error => {
                console.error('VdoCipher initialization error:', error);
                showVdoCipherError('Unable to load video player. Please refresh the page.');
            });
        }

        function showVdoCipherError(message) {
            const player = document.getElementById('vdocipher-player');
            player.innerHTML = `
                <div class=\"vdocipher-loading\">
                    <i class=\"fas fa-exclamation-triangle text-warning\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                    <h4>Video Loading Error</h4>
                    <p>\${message}</p>
                    <button class=\"btn btn-primary mt-3\" onclick=\"initializeVdoCipherPlayer()\">
                        <i class=\"fas fa-redo\"></i> Retry
                    </button>
                </div>
            `;
        }
        {% endif %}

        // Video Purchase Function
        function purchaseVideo(videoId) {
            // Show loading state
            const button = event.target.closest('.purchase-button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Processing...';
            button.disabled = true;

            // Create Stripe checkout session
            fetch('{{ path('app_video_purchase') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    video_id: videoId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.checkout_url) {
                    // Redirect to Stripe checkout
                    window.location.href = data.checkout_url;
                } else {
                    // Show error message
                    alert(data.message || 'Unable to process purchase. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Purchase error:', error);
                alert('Unable to process purchase. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }

        // Video Analytics Tracking
        {% if has_access %}
        document.addEventListener('DOMContentLoaded', function() {
            // Track video view
            fetch('{{ path('app_video_track_view', {'slug': video.slug}) }}', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            // Track video engagement
            let watchTime = 0;
            const trackingInterval = setInterval(() => {
                watchTime += 5; // Track every 5 seconds

                // Send tracking data every 30 seconds
                if (watchTime % 30 === 0) {
                    fetch('{{ path('app_video_track_engagement', {'slug': video.slug}) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            watch_time: watchTime
                        })
                    });
                }
            }, 5000);

            // Stop tracking when user leaves page
            window.addEventListener('beforeunload', () => {
                clearInterval(trackingInterval);
            });
        });
        {% endif %}

        // Smooth scroll for related videos
        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add to Cart Function
        function addToCart(type, id) {
            const button = event.target.closest('.add-to-cart-button');
            const originalContent = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Adding...';
            button.disabled = true;

            fetch('{{ path('app_cart_add') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `type=\${type}&id=\${id}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    button.innerHTML = '<i class=\"fas fa-check\"></i> Added to Cart!';
                    button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

                    // Update cart display
                    if (typeof updateCartBadge === 'function') {
                        updateCartBadge(data.cart.item_count);
                    }
                    if (typeof loadCartContents === 'function') {
                        loadCartContents();
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                        button.style.background = '';
                    }, 2000);
                } else {
                    // Show error message
                    alert(data.message || 'Unable to add item to cart. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Add to cart error:', error);
                alert('Unable to add item to cart. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }
    </script>

    <!-- VdoCipher Player Script -->
    {% if has_access and video.videoSourceType == 'vdocipher' %}
    <script src=\"https://player.vdocipher.com/v2/api.js\"></script>
    {% endif %}
{% endblock %}", "video/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\video\\show.html.twig");
    }
}
