<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* video/show.html.twig */
class __TwigTemplate_ff53fff703f82da28fc34ce1d34c40a2 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "video/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "video/show.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 3, $this->source); })()), "title", [], "any", false, false, false, 3), "html", null, true);
        yield " - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "description", [], "any", false, false, false, 5)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "description", [], "any", false, false, false, 5), 0, 160), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((("Watch " . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "title", [], "any", false, false, false, 5)) . " on Capitol Academy - Professional Trading Education"), "html", null, true)));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        /* Capitol Academy Video Detail Page Styles */
        .video-detail-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding-top: 100px;
            padding-bottom: 60px;
            min-height: 100vh;
        }

        .video-main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(1, 26, 45, 0.15);
            overflow: hidden;
            margin-bottom: 30px;
            max-width: 100%;
        }

        .video-player-section {
            background: #000;
            position: relative;
        }

        .video-player-wrapper {
            position: relative;
            width: 100%;
            background: #000;
        }

        .video-info-section {
            padding: 40px 60px;
        }

        @media (max-width: 768px) {
            .video-info-section {
                padding: 30px 20px;
            }
        }

        .video-sidebar {
            padding-left: 30px;
        }

        .price-card {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.2);
            margin-bottom: 20px;
        }

        .price-amount {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .price-duration {
            font-size: 1rem;
            color: #a8b3c7;
            margin-bottom: 0;
        }

        @media (max-width: 991px) {
            .video-sidebar {
                padding-left: 0;
                margin-top: 30px;
            }
        }

        .video-player {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-player iframe,
        .video-player video {
            width: 100%;
            height: 100%;
            border: none;
        }

        .video-description {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #343a40;
            margin-bottom: 30px;
        }

        .video-title {
            color: #011a2d;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
            align-items: center;
        }

        .video-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .badge-free {
            background: #28a745;
            color: white;
        }

        .badge-premium {
            background: #a90418;
            color: white;
        }

        .badge-login-required {
            background: #011a2d;
            color: white;
        }

        .video-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .video-description {
            color: #343a40;
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .access-denied-card {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-top: 30px;
        }

        .access-denied-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .purchase-button {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .purchase-button:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(169, 4, 24, 0.3);
            color: white;
            text-decoration: none;
        }

        .login-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
            color: white;
            text-decoration: none;
        }

        .premium-video-info {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
        }

        .price-info {
            margin-bottom: 20px;
        }

        .price-display {
            font-size: 2rem;
            font-weight: 700;
            color: #a90418;
            margin-bottom: 8px;
            font-family: 'Montserrat', sans-serif;
        }

        .access-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .add-to-cart-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
        }

        .add-to-cart-button:hover {
            background: linear-gradient(135deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .add-to-cart-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .go-to-cart-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
            text-decoration: none;
        }

        .go-to-cart-button:hover {
            background: linear-gradient(135deg, #1a3461, #2a4a7a);
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
        }

        .related-videos-section {
            background: #f8f9fa;
            padding: 60px 0;
        }

        .related-video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .related-video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .related-video-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .related-video-content {
            padding: 20px;
        }

        .related-video-title {
            color: #011a2d;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .vdocipher-player {
            background: #000;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
            position: relative;
        }

        .vdocipher-loading {
            color: white;
            text-align: center;
        }

        .video-source-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .video-hero-section {
                padding-top: 60px;
                min-height: auto;
            }

            .video-player {
                height: 300px;
            }

            .video-title {
                font-size: 2rem;
            }

            .video-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 392
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 393
        yield "<!-- Video Detail Section -->
<section class=\"video-detail-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-12\">
                <!-- Video Main Container -->
                <div class=\"video-main-container\">
                    <!-- Video Player Section -->
                    <div class=\"video-player-section\">
                        <div class=\"video-player-wrapper\">
                            <div class=\"video-player\">
                            ";
        // line 404
        if ((($tmp = (isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 404, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 405
            yield "                                <!-- Video Source Indicator -->
                                <div class=\"video-source-indicator\">
                                    ";
            // line 407
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 407, $this->source); })()), "videoSourceType", [], "any", false, false, false, 407) == "youtube")) {
                // line 408
                yield "                                        <i class=\"fab fa-youtube\"></i> YouTube
                                    ";
            } elseif ((CoreExtension::getAttribute($this->env, $this->source,             // line 409
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 409, $this->source); })()), "videoSourceType", [], "any", false, false, false, 409) == "vdocipher")) {
                // line 410
                yield "                                        <i class=\"fas fa-shield-alt\"></i> VdoCipher
                                    ";
            } else {
                // line 412
                yield "                                        <i class=\"fas fa-play\"></i> Direct
                                    ";
            }
            // line 414
            yield "                                </div>

                                <!-- YouTube Video Player -->
                                ";
            // line 417
            if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 417, $this->source); })()), "videoSourceType", [], "any", false, false, false, 417) == "youtube") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 417, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 417))) {
                // line 418
                yield "                                    <iframe src=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 418, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 418), "html", null, true);
                yield "\"
                                            frameborder=\"0\"
                                            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                            allowfullscreen>
                                    </iframe>

                                <!-- VdoCipher Video Player -->
                                ";
            } elseif (((CoreExtension::getAttribute($this->env, $this->source,             // line 425
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 425, $this->source); })()), "videoSourceType", [], "any", false, false, false, 425) == "vdocipher") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 425, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 425))) {
                // line 426
                yield "                                    <div class=\"vdocipher-player\" id=\"vdocipher-player\">
                                        <div class=\"vdocipher-loading\">
                                            <i class=\"fas fa-spinner fa-spin\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Loading Secure Video...</h4>
                                            <p>Initializing DRM-protected playback</p>
                                        </div>
                                    </div>

                                <!-- Direct Upload Video Player -->
                                ";
            } elseif ((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 435
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 435, $this->source); })()), "videoFile", [], "any", false, false, false, 435)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 436
                yield "                                    <video controls preload=\"metadata\" poster=\"";
                yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 436, $this->source); })()), "thumbnail", [], "any", false, false, false, 436)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 436, $this->source); })()), "thumbnail", [], "any", false, false, false, 436))), "html", null, true)) : (""));
                yield "\">
                                        <source src=\"";
                // line 437
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/files/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 437, $this->source); })()), "videoFile", [], "any", false, false, false, 437))), "html", null, true);
                yield "\" type=\"video/mp4\">
                                        <p>Your browser doesn't support HTML5 video. <a href=\"";
                // line 438
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/files/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 438, $this->source); })()), "videoFile", [], "any", false, false, false, 438))), "html", null, true);
                yield "\">Download the video</a> instead.</p>
                                    </video>

                                <!-- No Video Source -->
                                ";
            } else {
                // line 443
                yield "                                    <div class=\"d-flex align-items-center justify-content-center h-100\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-exclamation-triangle\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Video Not Available</h4>
                                            <p>This video is currently being processed. Please try again later.</p>
                                        </div>
                                    </div>
                                ";
            }
            // line 451
            yield "                            ";
        } else {
            // line 452
            yield "                                <!-- Access Denied - Show Thumbnail with Overlay -->
                                <div class=\"d-flex align-items-center justify-content-center h-100\" style=\"background: ";
            // line 453
            yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 453, $this->source); })()), "thumbnail", [], "any", false, false, false, 453)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((("url(" . $this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 453, $this->source); })()), "thumbnail", [], "any", false, false, false, 453)))) . ")"), "html", null, true)) : ("#000"));
            yield " center/cover;\">
                                    <div style=\"background: rgba(0, 0, 0, 0.8); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-lock\" style=\"font-size: 4rem; margin-bottom: 1rem;\"></i>
                                            <h3>Premium Content</h3>
                                            <p class=\"mb-0\">This video requires access to view</p>
                                        </div>
                                    </div>
                                </div>
                            ";
        }
        // line 463
        yield "                        </div>
                    </div>

                    </div>

                    <!-- Video Information Section -->
                    <div class=\"video-info-section\">
                        <div class=\"row\">
                            <div class=\"col-lg-8\">
                                <h1 class=\"video-title\">";
        // line 472
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 472, $this->source); })()), "title", [], "any", false, false, false, 472), "html", null, true);
        yield "</h1>

                        <!-- Video Meta Information -->
                        <div class=\"video-meta\">
                            <!-- Access Level Badge -->
                            ";
        // line 477
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 477, $this->source); })()), "accessLevel", [], "any", false, false, false, 477) == "public_free")) {
            // line 478
            yield "                                <span class=\"video-badge badge-free\">
                                    <i class=\"fas fa-globe\"></i>
                                    Public Free
                                </span>
                            ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 482
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 482, $this->source); })()), "accessLevel", [], "any", false, false, false, 482) == "login_required_free")) {
            // line 483
            yield "                                <span class=\"video-badge badge-login-required\">
                                    <i class=\"fas fa-user\"></i>
                                    Login Required
                                </span>
                            ";
        } else {
            // line 488
            yield "                                <span class=\"video-badge badge-premium\">
                                    <i class=\"fas fa-crown\"></i>
                                    Premium - ";
            // line 490
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 490, $this->source); })()), "formattedPrice", [], "any", false, false, false, 490), "html", null, true);
            yield "
                                    ";
            // line 491
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 491, $this->source); })()), "accessDuration", [], "any", false, false, false, 491)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 492
                yield "                                        (";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 492, $this->source); })()), "formattedAccessDuration", [], "any", false, false, false, 492), "html", null, true);
                yield ")
                                    ";
            }
            // line 494
            yield "                                </span>
                            ";
        }
        // line 496
        yield "
                            <!-- Category Badge -->
                            ";
        // line 498
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 498, $this->source); })()), "category", [], "any", false, false, false, 498)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 499
            yield "                                <span class=\"video-badge\" style=\"background: #6c757d; color: white;\">
                                    <i class=\"fas fa-tag\"></i>
                                    ";
            // line 501
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 501, $this->source); })()), "category", [], "any", false, false, false, 501), "html", null, true);
            yield "
                                </span>
                            ";
        }
        // line 504
        yield "
                            <!-- Video Source Badge -->
                            <span class=\"video-badge\" style=\"background: #17a2b8; color: white;\">
                                ";
        // line 507
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 507, $this->source); })()), "videoSourceType", [], "any", false, false, false, 507) == "youtube")) {
            // line 508
            yield "                                    <i class=\"fab fa-youtube\"></i>
                                    YouTube
                                ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 510
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 510, $this->source); })()), "videoSourceType", [], "any", false, false, false, 510) == "vdocipher")) {
            // line 511
            yield "                                    <i class=\"fas fa-shield-alt\"></i>
                                    VdoCipher
                                ";
        } else {
            // line 514
            yield "                                    <i class=\"fas fa-upload\"></i>
                                    Direct Upload
                                ";
        }
        // line 517
        yield "                            </span>

                            <!-- Upload Date -->
                            <span class=\"video-date\">
                                <i class=\"fas fa-calendar me-1\"></i>
                                ";
        // line 522
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 522, $this->source); })()), "createdAt", [], "any", false, false, false, 522), "M d, Y"), "html", null, true);
        yield "
                            </span>
                        </div>

                                <!-- Video Description -->
                                ";
        // line 527
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 527, $this->source); })()), "description", [], "any", false, false, false, 527)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 528
            yield "                                    <div class=\"video-description\">
                                        ";
            // line 529
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 529, $this->source); })()), "description", [], "any", false, false, false, 529), "html", null, true));
            yield "
                                    </div>
                                ";
        }
        // line 532
        yield "                            </div>

                            <div class=\"col-lg-4\">
                                <div class=\"video-sidebar\">
                                    ";
        // line 536
        if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 536, $this->source); })()), "accessLevel", [], "any", false, false, false, 536) == "premium") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 536, $this->source); })()), "price", [], "any", false, false, false, 536))) {
            // line 537
            yield "                                        <div class=\"price-card\">
                                            <div class=\"price-header\">
                                                <h3 class=\"price-amount\">\$";
            // line 539
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 539, $this->source); })()), "price", [], "any", false, false, false, 539), "html", null, true);
            yield "</h3>
                                                ";
            // line 540
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 540, $this->source); })()), "accessDuration", [], "any", false, false, false, 540)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 541
                yield "                                                    <p class=\"price-duration\">";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 541, $this->source); })()), "accessDuration", [], "any", false, false, false, 541), "html", null, true);
                yield " days access</p>
                                                ";
            } else {
                // line 543
                yield "                                                    <p class=\"price-duration\">Lifetime access</p>
                                                ";
            }
            // line 545
            yield "                                            </div>
                                        </div>
                                    ";
        }
        // line 548
        yield "                                </div>
                            </div>
                        </div>

                        <!-- Access Control Actions -->
                        ";
        // line 553
        if ((($tmp =  !(isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 553, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 554
            yield "                            ";
            if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 554, $this->source); })()), "accessLevel", [], "any", false, false, false, 554) == "login_required_free") &&  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 554, $this->source); })()), "user", [], "any", false, false, false, 554))) {
                // line 555
                yield "                                <!-- Login Required for Free Video -->
                                <div class=\"text-center\">
                                    <p class=\"mb-3\">Please log in to watch this free video.</p>
                                    <a href=\"";
                // line 558
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
                yield "\" class=\"login-button\">
                                        <i class=\"fas fa-sign-in-alt\"></i>
                                        Login to Watch
                                    </a>
                                </div>
                            ";
            } elseif ((CoreExtension::getAttribute($this->env, $this->source,             // line 563
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 563, $this->source); })()), "accessLevel", [], "any", false, false, false, 563) == "premium")) {
                // line 564
                yield "                                <!-- Premium Video Purchase -->
                                <div class=\"text-center\">
                                    <div class=\"premium-video-info\">
                                        <div class=\"price-info mb-3\">
                                            <h4 class=\"price-display\">";
                // line 568
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 568, $this->source); })()), "formattedPrice", [], "any", false, false, false, 568), "html", null, true);
                yield "</h4>
                                            <p class=\"access-info\">
                                                ";
                // line 570
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 570, $this->source); })()), "accessDuration", [], "any", false, false, false, 570)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 571
                    yield "                                                    ";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 571, $this->source); })()), "accessDuration", [], "any", false, false, false, 571), "html", null, true);
                    yield " days access
                                                ";
                } else {
                    // line 573
                    yield "                                                    Lifetime access
                                                ";
                }
                // line 575
                yield "                                            </p>
                                        </div>
                                        ";
                // line 577
                if ((($tmp = (isset($context["is_in_cart"]) || array_key_exists("is_in_cart", $context) ? $context["is_in_cart"] : (function () { throw new RuntimeError('Variable "is_in_cart" does not exist.', 577, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 578
                    yield "                                            <a href=\"";
                    yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart");
                    yield "\" class=\"go-to-cart-button\">
                                                <i class=\"fas fa-shopping-cart\"></i>
                                                Go to Cart
                                            </a>
                                        ";
                } else {
                    // line 583
                    yield "                                            <button class=\"add-to-cart-button\" onclick=\"addToCart('video', ";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 583, $this->source); })()), "id", [], "any", false, false, false, 583), "html", null, true);
                    yield ")\">
                                                <i class=\"fas fa-cart-plus\"></i>
                                                Add to Cart
                                            </button>
                                        ";
                }
                // line 588
                yield "                                        ";
                if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 588, $this->source); })()), "user", [], "any", false, false, false, 588)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 589
                    yield "                                            <p class=\"mt-3 text-muted small\">
                                                <i class=\"fas fa-info-circle me-1\"></i>
                                                You'll be asked to login during checkout
                                            </p>
                                        ";
                }
                // line 594
                yield "                                    </div>
                                </div>
                            ";
            }
            // line 597
            yield "                        ";
        }
        // line 598
        yield "                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Videos Section -->
";
        // line 606
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["related_videos"]) || array_key_exists("related_videos", $context) ? $context["related_videos"] : (function () { throw new RuntimeError('Variable "related_videos" does not exist.', 606, $this->source); })())) > 0)) {
            // line 607
            yield "<section class=\"related-videos-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-12\">
                <h2 class=\"text-center mb-5\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 700;\">
                    Related Videos
                </h2>
            </div>
        </div>
        <div class=\"row g-4\">
            ";
            // line 617
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["related_videos"]) || array_key_exists("related_videos", $context) ? $context["related_videos"] : (function () { throw new RuntimeError('Variable "related_videos" does not exist.', 617, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["related_video"]) {
                // line 618
                yield "                <div class=\"col-lg-3 col-md-6\">
                    <div class=\"related-video-card\">
                        <a href=\"";
                // line 620
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_show", ["slug" => CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "slug", [], "any", false, false, false, 620)]), "html", null, true);
                yield "\" class=\"text-decoration-none\">
                            ";
                // line 621
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "thumbnail", [], "any", false, false, false, 621)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 622
                    yield "                                <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "thumbnail", [], "any", false, false, false, 622))), "html", null, true);
                    yield "\"
                                     alt=\"";
                    // line 623
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "title", [], "any", false, false, false, 623), "html", null, true);
                    yield "\"
                                     class=\"related-video-thumbnail\">
                            ";
                } else {
                    // line 626
                    yield "                                <div class=\"related-video-thumbnail d-flex align-items-center justify-content-center\" style=\"background: #f8f9fa;\">
                                    <i class=\"fas fa-video text-muted\" style=\"font-size: 3rem;\"></i>
                                </div>
                            ";
                }
                // line 630
                yield "                            <div class=\"related-video-content\">
                                <h5 class=\"related-video-title\">";
                // line 631
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "title", [], "any", false, false, false, 631), "html", null, true);
                yield "</h5>
                                <div class=\"d-flex justify-content-between align-items-center\">
                                    <small class=\"text-muted\">
                                        <i class=\"fas fa-calendar me-1\"></i>
                                        ";
                // line 635
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "createdAt", [], "any", false, false, false, 635), "M d, Y"), "html", null, true);
                yield "
                                    </small>
                                    ";
                // line 637
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "accessLevel", [], "any", false, false, false, 637) == "public_free")) {
                    // line 638
                    yield "                                        <span class=\"badge bg-success\">Free</span>
                                    ";
                } elseif ((CoreExtension::getAttribute($this->env, $this->source,                 // line 639
$context["related_video"], "accessLevel", [], "any", false, false, false, 639) == "login_required_free")) {
                    // line 640
                    yield "                                        <span class=\"badge bg-info\">Login Required</span>
                                    ";
                } else {
                    // line 642
                    yield "                                        <span class=\"badge bg-warning text-dark\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "formattedPrice", [], "any", false, false, false, 642), "html", null, true);
                    yield "</span>
                                    ";
                }
                // line 644
                yield "                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['related_video'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 650
            yield "        </div>
    </div>
</section>
";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 656
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 657
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        // VdoCipher Player Initialization
        ";
        // line 660
        if ((((isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 660, $this->source); })()) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 660, $this->source); })()), "videoSourceType", [], "any", false, false, false, 660) == "vdocipher")) && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 660, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 660))) {
            // line 661
            yield "        document.addEventListener('DOMContentLoaded', function() {
            initializeVdoCipherPlayer();
        });

        function initializeVdoCipherPlayer() {
            // Get video access token from server
            fetch('";
            // line 667
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_vdocipher_token", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 667, $this->source); })()), "id", [], "any", false, false, false, 667)]), "html", null, true);
            yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.otp && data.playbackInfo) {
                    // Initialize VdoCipher player
                    new VdoPlayer({
                        otp: data.otp,
                        playbackInfo: data.playbackInfo,
                        theme: \"9bb535\",
                        container: document.querySelector(\"#vdocipher-player\"),
                        width: \"100%\",
                        height: \"100%\"
                    });
                } else {
                    showVdoCipherError('Failed to load video. Please try again.');
                }
            })
            .catch(error => {
                console.error('VdoCipher initialization error:', error);
                showVdoCipherError('Unable to load video player. Please refresh the page.');
            });
        }

        function showVdoCipherError(message) {
            const player = document.getElementById('vdocipher-player');
            player.innerHTML = `
                <div class=\"vdocipher-loading\">
                    <i class=\"fas fa-exclamation-triangle text-warning\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                    <h4>Video Loading Error</h4>
                    <p>\${message}</p>
                    <button class=\"btn btn-primary mt-3\" onclick=\"initializeVdoCipherPlayer()\">
                        <i class=\"fas fa-redo\"></i> Retry
                    </button>
                </div>
            `;
        }
        ";
        }
        // line 710
        yield "
        // Video Purchase Function
        function purchaseVideo(videoId) {
            // Show loading state
            const button = event.target.closest('.purchase-button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Processing...';
            button.disabled = true;

            // Create Stripe checkout session
            fetch('";
        // line 720
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_purchase");
        yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    video_id: videoId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.checkout_url) {
                    // Redirect to Stripe checkout
                    window.location.href = data.checkout_url;
                } else {
                    // Show error message
                    alert(data.message || 'Unable to process purchase. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Purchase error:', error);
                alert('Unable to process purchase. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }

        // Video Analytics Tracking
        ";
        // line 751
        if ((($tmp = (isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 751, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 752
            yield "        document.addEventListener('DOMContentLoaded', function() {
            // Track video view
            fetch('";
            // line 754
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_track_view", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 754, $this->source); })()), "slug", [], "any", false, false, false, 754)]), "html", null, true);
            yield "', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            // Track video engagement
            let watchTime = 0;
            const trackingInterval = setInterval(() => {
                watchTime += 5; // Track every 5 seconds

                // Send tracking data every 30 seconds
                if (watchTime % 30 === 0) {
                    fetch('";
            // line 768
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_track_engagement", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 768, $this->source); })()), "slug", [], "any", false, false, false, 768)]), "html", null, true);
            yield "', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            watch_time: watchTime
                        })
                    });
                }
            }, 5000);

            // Stop tracking when user leaves page
            window.addEventListener('beforeunload', () => {
                clearInterval(trackingInterval);
            });
        });
        ";
        }
        // line 787
        yield "
        // Smooth scroll for related videos
        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add to Cart Function
        function addToCart(type, id) {
            const button = event.target.closest('.add-to-cart-button');
            const originalContent = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Adding...';
            button.disabled = true;

            fetch('";
        // line 811
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart_add");
        yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `type=\${type}&id=\${id}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.duplicate) {
                        // Item already in cart - show different message
                        button.innerHTML = '<i class=\"fas fa-check\"></i> Already in Cart';
                        button.style.background = 'linear-gradient(135deg, #6c757d, #5a6268)';
                    } else {
                        // Show success message
                        button.innerHTML = '<i class=\"fas fa-check\"></i> Added to Cart!';
                        button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                    }

                    // Update cart display
                    if (typeof updateCartBadge === 'function') {
                        updateCartBadge(data.cart.item_count);
                    }
                    if (typeof loadCartContents === 'function') {
                        loadCartContents();
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                        button.style.background = '';
                    }, 2000);
                } else {
                    // Show error message
                    alert(data.message || 'Unable to add item to cart. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Add to cart error:', error);
                alert('Unable to add item to cart. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }
    </script>

    <!-- VdoCipher Player Script -->
    ";
        // line 863
        if (((isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 863, $this->source); })()) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 863, $this->source); })()), "videoSourceType", [], "any", false, false, false, 863) == "vdocipher"))) {
            // line 864
            yield "    <script src=\"https://player.vdocipher.com/v2/api.js\"></script>
    ";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "video/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1257 => 864,  1255 => 863,  1200 => 811,  1174 => 787,  1152 => 768,  1135 => 754,  1131 => 752,  1129 => 751,  1095 => 720,  1083 => 710,  1037 => 667,  1029 => 661,  1027 => 660,  1020 => 657,  1007 => 656,  992 => 650,  981 => 644,  975 => 642,  971 => 640,  969 => 639,  966 => 638,  964 => 637,  959 => 635,  952 => 631,  949 => 630,  943 => 626,  937 => 623,  932 => 622,  930 => 621,  926 => 620,  922 => 618,  918 => 617,  906 => 607,  904 => 606,  894 => 598,  891 => 597,  886 => 594,  879 => 589,  876 => 588,  867 => 583,  858 => 578,  856 => 577,  852 => 575,  848 => 573,  842 => 571,  840 => 570,  835 => 568,  829 => 564,  827 => 563,  819 => 558,  814 => 555,  811 => 554,  809 => 553,  802 => 548,  797 => 545,  793 => 543,  787 => 541,  785 => 540,  781 => 539,  777 => 537,  775 => 536,  769 => 532,  763 => 529,  760 => 528,  758 => 527,  750 => 522,  743 => 517,  738 => 514,  733 => 511,  731 => 510,  727 => 508,  725 => 507,  720 => 504,  714 => 501,  710 => 499,  708 => 498,  704 => 496,  700 => 494,  694 => 492,  692 => 491,  688 => 490,  684 => 488,  677 => 483,  675 => 482,  669 => 478,  667 => 477,  659 => 472,  648 => 463,  635 => 453,  632 => 452,  629 => 451,  619 => 443,  611 => 438,  607 => 437,  602 => 436,  600 => 435,  589 => 426,  587 => 425,  576 => 418,  574 => 417,  569 => 414,  565 => 412,  561 => 410,  559 => 409,  556 => 408,  554 => 407,  550 => 405,  548 => 404,  535 => 393,  522 => 392,  127 => 8,  114 => 7,  91 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}{{ video.title }} - Capitol Academy{% endblock %}

{% block meta_description %}{{ video.description ? video.description|slice(0, 160) : 'Watch ' ~ video.title ~ ' on Capitol Academy - Professional Trading Education' }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Capitol Academy Video Detail Page Styles */
        .video-detail-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding-top: 100px;
            padding-bottom: 60px;
            min-height: 100vh;
        }

        .video-main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(1, 26, 45, 0.15);
            overflow: hidden;
            margin-bottom: 30px;
            max-width: 100%;
        }

        .video-player-section {
            background: #000;
            position: relative;
        }

        .video-player-wrapper {
            position: relative;
            width: 100%;
            background: #000;
        }

        .video-info-section {
            padding: 40px 60px;
        }

        @media (max-width: 768px) {
            .video-info-section {
                padding: 30px 20px;
            }
        }

        .video-sidebar {
            padding-left: 30px;
        }

        .price-card {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.2);
            margin-bottom: 20px;
        }

        .price-amount {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .price-duration {
            font-size: 1rem;
            color: #a8b3c7;
            margin-bottom: 0;
        }

        @media (max-width: 991px) {
            .video-sidebar {
                padding-left: 0;
                margin-top: 30px;
            }
        }

        .video-player {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-player iframe,
        .video-player video {
            width: 100%;
            height: 100%;
            border: none;
        }

        .video-description {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #343a40;
            margin-bottom: 30px;
        }

        .video-title {
            color: #011a2d;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
            align-items: center;
        }

        .video-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .badge-free {
            background: #28a745;
            color: white;
        }

        .badge-premium {
            background: #a90418;
            color: white;
        }

        .badge-login-required {
            background: #011a2d;
            color: white;
        }

        .video-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .video-description {
            color: #343a40;
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .access-denied-card {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-top: 30px;
        }

        .access-denied-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .purchase-button {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .purchase-button:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(169, 4, 24, 0.3);
            color: white;
            text-decoration: none;
        }

        .login-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
            color: white;
            text-decoration: none;
        }

        .premium-video-info {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
        }

        .price-info {
            margin-bottom: 20px;
        }

        .price-display {
            font-size: 2rem;
            font-weight: 700;
            color: #a90418;
            margin-bottom: 8px;
            font-family: 'Montserrat', sans-serif;
        }

        .access-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .add-to-cart-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
        }

        .add-to-cart-button:hover {
            background: linear-gradient(135deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .add-to-cart-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .go-to-cart-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
            text-decoration: none;
        }

        .go-to-cart-button:hover {
            background: linear-gradient(135deg, #1a3461, #2a4a7a);
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
        }

        .related-videos-section {
            background: #f8f9fa;
            padding: 60px 0;
        }

        .related-video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .related-video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .related-video-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .related-video-content {
            padding: 20px;
        }

        .related-video-title {
            color: #011a2d;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .vdocipher-player {
            background: #000;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
            position: relative;
        }

        .vdocipher-loading {
            color: white;
            text-align: center;
        }

        .video-source-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .video-hero-section {
                padding-top: 60px;
                min-height: auto;
            }

            .video-player {
                height: 300px;
            }

            .video-title {
                font-size: 2rem;
            }

            .video-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
{% endblock %}

{% block body %}
<!-- Video Detail Section -->
<section class=\"video-detail-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-12\">
                <!-- Video Main Container -->
                <div class=\"video-main-container\">
                    <!-- Video Player Section -->
                    <div class=\"video-player-section\">
                        <div class=\"video-player-wrapper\">
                            <div class=\"video-player\">
                            {% if has_access %}
                                <!-- Video Source Indicator -->
                                <div class=\"video-source-indicator\">
                                    {% if video.videoSourceType == 'youtube' %}
                                        <i class=\"fab fa-youtube\"></i> YouTube
                                    {% elseif video.videoSourceType == 'vdocipher' %}
                                        <i class=\"fas fa-shield-alt\"></i> VdoCipher
                                    {% else %}
                                        <i class=\"fas fa-play\"></i> Direct
                                    {% endif %}
                                </div>

                                <!-- YouTube Video Player -->
                                {% if video.videoSourceType == 'youtube' and video.youtubeEmbedUrl %}
                                    <iframe src=\"{{ video.youtubeEmbedUrl }}\"
                                            frameborder=\"0\"
                                            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                            allowfullscreen>
                                    </iframe>

                                <!-- VdoCipher Video Player -->
                                {% elseif video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                                    <div class=\"vdocipher-player\" id=\"vdocipher-player\">
                                        <div class=\"vdocipher-loading\">
                                            <i class=\"fas fa-spinner fa-spin\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Loading Secure Video...</h4>
                                            <p>Initializing DRM-protected playback</p>
                                        </div>
                                    </div>

                                <!-- Direct Upload Video Player -->
                                {% elseif video.videoFile %}
                                    <video controls preload=\"metadata\" poster=\"{{ video.thumbnail ? asset('uploads/videos/thumbnails/' ~ video.thumbnail) : '' }}\">
                                        <source src=\"{{ asset('uploads/videos/files/' ~ video.videoFile) }}\" type=\"video/mp4\">
                                        <p>Your browser doesn't support HTML5 video. <a href=\"{{ asset('uploads/videos/files/' ~ video.videoFile) }}\">Download the video</a> instead.</p>
                                    </video>

                                <!-- No Video Source -->
                                {% else %}
                                    <div class=\"d-flex align-items-center justify-content-center h-100\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-exclamation-triangle\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Video Not Available</h4>
                                            <p>This video is currently being processed. Please try again later.</p>
                                        </div>
                                    </div>
                                {% endif %}
                            {% else %}
                                <!-- Access Denied - Show Thumbnail with Overlay -->
                                <div class=\"d-flex align-items-center justify-content-center h-100\" style=\"background: {{ video.thumbnail ? 'url(' ~ asset('uploads/videos/thumbnails/' ~ video.thumbnail) ~ ')' : '#000' }} center/cover;\">
                                    <div style=\"background: rgba(0, 0, 0, 0.8); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-lock\" style=\"font-size: 4rem; margin-bottom: 1rem;\"></i>
                                            <h3>Premium Content</h3>
                                            <p class=\"mb-0\">This video requires access to view</p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    </div>

                    <!-- Video Information Section -->
                    <div class=\"video-info-section\">
                        <div class=\"row\">
                            <div class=\"col-lg-8\">
                                <h1 class=\"video-title\">{{ video.title }}</h1>

                        <!-- Video Meta Information -->
                        <div class=\"video-meta\">
                            <!-- Access Level Badge -->
                            {% if video.accessLevel == 'public_free' %}
                                <span class=\"video-badge badge-free\">
                                    <i class=\"fas fa-globe\"></i>
                                    Public Free
                                </span>
                            {% elseif video.accessLevel == 'login_required_free' %}
                                <span class=\"video-badge badge-login-required\">
                                    <i class=\"fas fa-user\"></i>
                                    Login Required
                                </span>
                            {% else %}
                                <span class=\"video-badge badge-premium\">
                                    <i class=\"fas fa-crown\"></i>
                                    Premium - {{ video.formattedPrice }}
                                    {% if video.accessDuration %}
                                        ({{ video.formattedAccessDuration }})
                                    {% endif %}
                                </span>
                            {% endif %}

                            <!-- Category Badge -->
                            {% if video.category %}
                                <span class=\"video-badge\" style=\"background: #6c757d; color: white;\">
                                    <i class=\"fas fa-tag\"></i>
                                    {{ video.category }}
                                </span>
                            {% endif %}

                            <!-- Video Source Badge -->
                            <span class=\"video-badge\" style=\"background: #17a2b8; color: white;\">
                                {% if video.videoSourceType == 'youtube' %}
                                    <i class=\"fab fa-youtube\"></i>
                                    YouTube
                                {% elseif video.videoSourceType == 'vdocipher' %}
                                    <i class=\"fas fa-shield-alt\"></i>
                                    VdoCipher
                                {% else %}
                                    <i class=\"fas fa-upload\"></i>
                                    Direct Upload
                                {% endif %}
                            </span>

                            <!-- Upload Date -->
                            <span class=\"video-date\">
                                <i class=\"fas fa-calendar me-1\"></i>
                                {{ video.createdAt|date('M d, Y') }}
                            </span>
                        </div>

                                <!-- Video Description -->
                                {% if video.description %}
                                    <div class=\"video-description\">
                                        {{ video.description|nl2br }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class=\"col-lg-4\">
                                <div class=\"video-sidebar\">
                                    {% if video.accessLevel == 'premium' and video.price %}
                                        <div class=\"price-card\">
                                            <div class=\"price-header\">
                                                <h3 class=\"price-amount\">\${{ video.price }}</h3>
                                                {% if video.accessDuration %}
                                                    <p class=\"price-duration\">{{ video.accessDuration }} days access</p>
                                                {% else %}
                                                    <p class=\"price-duration\">Lifetime access</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Access Control Actions -->
                        {% if not has_access %}
                            {% if video.accessLevel == 'login_required_free' and not app.user %}
                                <!-- Login Required for Free Video -->
                                <div class=\"text-center\">
                                    <p class=\"mb-3\">Please log in to watch this free video.</p>
                                    <a href=\"{{ path('app_login') }}\" class=\"login-button\">
                                        <i class=\"fas fa-sign-in-alt\"></i>
                                        Login to Watch
                                    </a>
                                </div>
                            {% elseif video.accessLevel == 'premium' %}
                                <!-- Premium Video Purchase -->
                                <div class=\"text-center\">
                                    <div class=\"premium-video-info\">
                                        <div class=\"price-info mb-3\">
                                            <h4 class=\"price-display\">{{ video.formattedPrice }}</h4>
                                            <p class=\"access-info\">
                                                {% if video.accessDuration %}
                                                    {{ video.accessDuration }} days access
                                                {% else %}
                                                    Lifetime access
                                                {% endif %}
                                            </p>
                                        </div>
                                        {% if is_in_cart %}
                                            <a href=\"{{ path('app_cart') }}\" class=\"go-to-cart-button\">
                                                <i class=\"fas fa-shopping-cart\"></i>
                                                Go to Cart
                                            </a>
                                        {% else %}
                                            <button class=\"add-to-cart-button\" onclick=\"addToCart('video', {{ video.id }})\">
                                                <i class=\"fas fa-cart-plus\"></i>
                                                Add to Cart
                                            </button>
                                        {% endif %}
                                        {% if not app.user %}
                                            <p class=\"mt-3 text-muted small\">
                                                <i class=\"fas fa-info-circle me-1\"></i>
                                                You'll be asked to login during checkout
                                            </p>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Videos Section -->
{% if related_videos|length > 0 %}
<section class=\"related-videos-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-12\">
                <h2 class=\"text-center mb-5\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 700;\">
                    Related Videos
                </h2>
            </div>
        </div>
        <div class=\"row g-4\">
            {% for related_video in related_videos %}
                <div class=\"col-lg-3 col-md-6\">
                    <div class=\"related-video-card\">
                        <a href=\"{{ path('app_video_show', {'slug': related_video.slug}) }}\" class=\"text-decoration-none\">
                            {% if related_video.thumbnail %}
                                <img src=\"{{ asset('uploads/videos/thumbnails/' ~ related_video.thumbnail) }}\"
                                     alt=\"{{ related_video.title }}\"
                                     class=\"related-video-thumbnail\">
                            {% else %}
                                <div class=\"related-video-thumbnail d-flex align-items-center justify-content-center\" style=\"background: #f8f9fa;\">
                                    <i class=\"fas fa-video text-muted\" style=\"font-size: 3rem;\"></i>
                                </div>
                            {% endif %}
                            <div class=\"related-video-content\">
                                <h5 class=\"related-video-title\">{{ related_video.title }}</h5>
                                <div class=\"d-flex justify-content-between align-items-center\">
                                    <small class=\"text-muted\">
                                        <i class=\"fas fa-calendar me-1\"></i>
                                        {{ related_video.createdAt|date('M d, Y') }}
                                    </small>
                                    {% if related_video.accessLevel == 'public_free' %}
                                        <span class=\"badge bg-success\">Free</span>
                                    {% elseif related_video.accessLevel == 'login_required_free' %}
                                        <span class=\"badge bg-info\">Login Required</span>
                                    {% else %}
                                        <span class=\"badge bg-warning text-dark\">{{ related_video.formattedPrice }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // VdoCipher Player Initialization
        {% if has_access and video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
        document.addEventListener('DOMContentLoaded', function() {
            initializeVdoCipherPlayer();
        });

        function initializeVdoCipherPlayer() {
            // Get video access token from server
            fetch('{{ path('app_video_vdocipher_token', {'id': video.id}) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.otp && data.playbackInfo) {
                    // Initialize VdoCipher player
                    new VdoPlayer({
                        otp: data.otp,
                        playbackInfo: data.playbackInfo,
                        theme: \"9bb535\",
                        container: document.querySelector(\"#vdocipher-player\"),
                        width: \"100%\",
                        height: \"100%\"
                    });
                } else {
                    showVdoCipherError('Failed to load video. Please try again.');
                }
            })
            .catch(error => {
                console.error('VdoCipher initialization error:', error);
                showVdoCipherError('Unable to load video player. Please refresh the page.');
            });
        }

        function showVdoCipherError(message) {
            const player = document.getElementById('vdocipher-player');
            player.innerHTML = `
                <div class=\"vdocipher-loading\">
                    <i class=\"fas fa-exclamation-triangle text-warning\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                    <h4>Video Loading Error</h4>
                    <p>\${message}</p>
                    <button class=\"btn btn-primary mt-3\" onclick=\"initializeVdoCipherPlayer()\">
                        <i class=\"fas fa-redo\"></i> Retry
                    </button>
                </div>
            `;
        }
        {% endif %}

        // Video Purchase Function
        function purchaseVideo(videoId) {
            // Show loading state
            const button = event.target.closest('.purchase-button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Processing...';
            button.disabled = true;

            // Create Stripe checkout session
            fetch('{{ path('app_video_purchase') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    video_id: videoId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.checkout_url) {
                    // Redirect to Stripe checkout
                    window.location.href = data.checkout_url;
                } else {
                    // Show error message
                    alert(data.message || 'Unable to process purchase. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Purchase error:', error);
                alert('Unable to process purchase. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }

        // Video Analytics Tracking
        {% if has_access %}
        document.addEventListener('DOMContentLoaded', function() {
            // Track video view
            fetch('{{ path('app_video_track_view', {'slug': video.slug}) }}', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            // Track video engagement
            let watchTime = 0;
            const trackingInterval = setInterval(() => {
                watchTime += 5; // Track every 5 seconds

                // Send tracking data every 30 seconds
                if (watchTime % 30 === 0) {
                    fetch('{{ path('app_video_track_engagement', {'slug': video.slug}) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            watch_time: watchTime
                        })
                    });
                }
            }, 5000);

            // Stop tracking when user leaves page
            window.addEventListener('beforeunload', () => {
                clearInterval(trackingInterval);
            });
        });
        {% endif %}

        // Smooth scroll for related videos
        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add to Cart Function
        function addToCart(type, id) {
            const button = event.target.closest('.add-to-cart-button');
            const originalContent = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Adding...';
            button.disabled = true;

            fetch('{{ path('app_cart_add') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `type=\${type}&id=\${id}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.duplicate) {
                        // Item already in cart - show different message
                        button.innerHTML = '<i class=\"fas fa-check\"></i> Already in Cart';
                        button.style.background = 'linear-gradient(135deg, #6c757d, #5a6268)';
                    } else {
                        // Show success message
                        button.innerHTML = '<i class=\"fas fa-check\"></i> Added to Cart!';
                        button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';
                    }

                    // Update cart display
                    if (typeof updateCartBadge === 'function') {
                        updateCartBadge(data.cart.item_count);
                    }
                    if (typeof loadCartContents === 'function') {
                        loadCartContents();
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                        button.style.background = '';
                    }, 2000);
                } else {
                    // Show error message
                    alert(data.message || 'Unable to add item to cart. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Add to cart error:', error);
                alert('Unable to add item to cart. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }
    </script>

    <!-- VdoCipher Player Script -->
    {% if has_access and video.videoSourceType == 'vdocipher' %}
    <script src=\"https://player.vdocipher.com/v2/api.js\"></script>
    {% endif %}
{% endblock %}", "video/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\video\\show.html.twig");
    }
}
