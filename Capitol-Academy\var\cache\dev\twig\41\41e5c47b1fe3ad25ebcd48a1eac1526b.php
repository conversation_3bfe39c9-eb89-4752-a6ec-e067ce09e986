<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/video/show.html.twig */
class __TwigTemplate_278483c31be13689339ad47e257f05e0 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/show.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Video Details - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Video Details";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"";
        // line 9
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_index");
        yield "\">Videos</a></li>
<li class=\"breadcrumb-item active\">";
        // line 10
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 10, $this->source); })()), "title", [], "any", false, false, false, 10), "html", null, true);
        yield "</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 13
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 14
        yield "<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-video mr-3\" style=\"font-size: 2rem;\"></i>
                        Video Details: ";
        // line 24
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 24, $this->source); })()), "title", [], "any", false, false, false, 24), "html", null, true);
        yield "
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Video Button (Icon Only) -->
                        <a href=\"";
        // line 30
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_edit", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 30, $this->source); })()), "slug", [], "any", false, false, false, 30)]), "html", null, true);
        yield "\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Video\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Video Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Video Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Videos Button -->
                        <a href=\"";
        // line 50
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_index");
        yield "\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
            <div class=\"row\">
                <div class=\"col-12\">
                    <!-- Video Title and Category (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-8\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-video text-primary mr-1\"></i>
                                    Video Title
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 75
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 75, $this->source); })()), "title", [], "any", false, false, false, 75), "html", null, true);
        yield "
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-tag text-primary mr-1\"></i>
                                    Category
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 86
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["video"] ?? null), "category", [], "any", true, true, false, 86) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 86, $this->source); })()), "category", [], "any", false, false, false, 86)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 86, $this->source); })()), "category", [], "any", false, false, false, 86), "html", null, true)) : ("Uncategorized"));
        yield "
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-align-left text-primary mr-1\"></i>
                            Description
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;\">
                            ";
        // line 99
        yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["video"] ?? null), "description", [], "any", true, true, false, 99) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 99, $this->source); })()), "description", [], "any", false, false, false, 99)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 99, $this->source); })()), "description", [], "any", false, false, false, 99), "html", null, true)) : ("No description provided"));
        yield "
                        </div>
                    </div>

                    <!-- Video Source Type and Access Level (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-source text-primary mr-1\"></i>
                                    Video Source Type
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 112
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 112, $this->source); })()), "videoSourceType", [], "any", false, false, false, 112) == "youtube")) {
            // line 113
            yield "                                        <i class=\"fab fa-youtube text-danger mr-2\"></i>YouTube
                                    ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 114
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 114, $this->source); })()), "videoSourceType", [], "any", false, false, false, 114) == "vdocipher")) {
            // line 115
            yield "                                        <i class=\"fas fa-shield-alt text-success mr-2\"></i>VdoCipher
                                    ";
        } else {
            // line 117
            yield "                                        <i class=\"fas fa-upload text-info mr-2\"></i>Direct Upload
                                    ";
        }
        // line 119
        yield "                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-lock text-primary mr-1\"></i>
                                    Access Level
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
        // line 129
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 129, $this->source); })()), "accessLevel", [], "any", false, false, false, 129) == "public_free")) {
            // line 130
            yield "                                        <i class=\"fas fa-globe text-success mr-2\"></i>Public Free
                                    ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 131
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 131, $this->source); })()), "accessLevel", [], "any", false, false, false, 131) == "login_required_free")) {
            // line 132
            yield "                                        <i class=\"fas fa-user text-warning mr-2\"></i>Login Required Free
                                    ";
        } else {
            // line 134
            yield "                                        <i class=\"fas fa-crown text-danger mr-2\"></i>Premium
                                    ";
        }
        // line 136
        yield "                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Pricing (if premium) -->
                    ";
        // line 142
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 142, $this->source); })()), "accessLevel", [], "any", false, false, false, 142) == "premium")) {
            // line 143
            yield "                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                    Price
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 600;\">
                                    \$";
            // line 151
            yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["video"] ?? null), "price", [], "any", true, true, false, 151) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 151, $this->source); })()), "price", [], "any", false, false, false, 151)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 151, $this->source); })()), "price", [], "any", false, false, false, 151), "html", null, true)) : ("0.00"));
            yield "
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-clock text-primary mr-1\"></i>
                                    Access Duration in Days
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    ";
            // line 162
            yield (((CoreExtension::getAttribute($this->env, $this->source, ($context["video"] ?? null), "accessDuration", [], "any", true, true, false, 162) &&  !(null === CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 162, $this->source); })()), "accessDuration", [], "any", false, false, false, 162)))) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 162, $this->source); })()), "accessDuration", [], "any", false, false, false, 162), "html", null, true)) : ("Unlimited"));
            yield " ";
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 162, $this->source); })()), "accessDuration", [], "any", false, false, false, 162)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                yield "days";
            }
            // line 163
            yield "                                </div>
                            </div>
                        </div>
                    </div>
                    ";
        }
        // line 168
        yield "
                    <!-- YouTube URL (if YouTube) -->
                    ";
        // line 170
        if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 170, $this->source); })()), "videoSourceType", [], "any", false, false, false, 170) == "youtube") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 170, $this->source); })()), "youtubeUrl", [], "any", false, false, false, 170))) {
            // line 171
            yield "                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fab fa-youtube text-danger mr-1\"></i>
                            YouTube URL
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <a href=\"";
            // line 177
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 177, $this->source); })()), "youtubeUrl", [], "any", false, false, false, 177), "html", null, true);
            yield "\" target=\"_blank\" class=\"text-decoration-none\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 177, $this->source); })()), "youtubeUrl", [], "any", false, false, false, 177), "html", null, true);
            yield "</a>
                        </div>
                    </div>
                    ";
        }
        // line 181
        yield "
                    <!-- VdoCipher ID (if VdoCipher) -->
                    ";
        // line 183
        if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 183, $this->source); })()), "videoSourceType", [], "any", false, false, false, 183) == "vdocipher") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 183, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 183))) {
            // line 184
            yield "                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-shield-alt text-success mr-1\"></i>
                            VdoCipher ID
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-family: monospace;\">
                            ";
            // line 190
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 190, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 190), "html", null, true);
            yield "
                        </div>
                    </div>
                    ";
        }
        // line 194
        yield "                    <!-- Thumbnail and Video (same line, equal height and width) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-image text-primary mr-1\"></i>
                                    Thumbnail
                                </label>
                                <div class=\"d-flex justify-content-center\">
                                    ";
        // line 203
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 203, $this->source); })()), "thumbnail", [], "any", false, false, false, 203)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 204
            yield "                                        <img src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 204, $this->source); })()), "thumbnail", [], "any", false, false, false, 204))), "html", null, true);
            yield "\"
                                             alt=\"Video Thumbnail\"
                                             class=\"img-fluid\"
                                             style=\"width: 400px; height: 225px; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                                    ";
        } else {
            // line 209
            yield "                                        <div class=\"d-flex align-items-center justify-content-center\"
                                             style=\"width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                            <div class=\"text-center text-muted\">
                                                <i class=\"fas fa-image\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                <p>No thumbnail available</p>
                                            </div>
                                        </div>
                                    ";
        }
        // line 217
        yield "                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-play text-primary mr-1\"></i>
                                    Video Player
                                </label>
                                <div class=\"d-flex justify-content-center\">
                                    ";
        // line 227
        if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 227, $this->source); })()), "videoSourceType", [], "any", false, false, false, 227) == "youtube") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 227, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 227))) {
            // line 228
            yield "                                        <iframe src=\"";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 228, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 228), "html", null, true);
            yield "\"
                                                style=\"width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\"
                                                frameborder=\"0\"
                                                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                                allowfullscreen>
                                        </iframe>
                                    ";
        } elseif (((CoreExtension::getAttribute($this->env, $this->source,         // line 234
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 234, $this->source); })()), "videoSourceType", [], "any", false, false, false, 234) == "vdocipher") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 234, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 234))) {
            // line 235
            yield "                                        <div class=\"d-flex align-items-center justify-content-center\"
                                             style=\"width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); background: #f8f9fa;\">
                                            <div class=\"text-center\">
                                                <i class=\"fas fa-shield-alt text-success\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                <h5>VdoCipher Protected Video</h5>
                                                <p class=\"text-muted\">ID: ";
            // line 240
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 240, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 240), "html", null, true);
            yield "</p>
                                            </div>
                                        </div>
                                    ";
        } elseif ((($tmp = CoreExtension::getAttribute($this->env, $this->source,         // line 243
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 243, $this->source); })()), "videoFile", [], "any", false, false, false, 243)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 244
            yield "                                        <video controls
                                               style=\"width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                                            <source src=\"";
            // line 246
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/files/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 246, $this->source); })()), "videoFile", [], "any", false, false, false, 246))), "html", null, true);
            yield "\" type=\"video/mp4\">
                                            Your browser does not support the video tag.
                                        </video>
                                    ";
        } else {
            // line 250
            yield "                                        <div class=\"d-flex align-items-center justify-content-center\"
                                             style=\"width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                            <div class=\"text-center text-muted\">
                                                <i class=\"fas fa-video\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                <p>No video available</p>
                                            </div>
                                        </div>
                                    ";
        }
        // line 258
        yield "                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Status and Creation Date -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                    Status
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    ";
        // line 271
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 271, $this->source); })()), "isActive", [], "any", false, false, false, 271)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 272
            yield "                                        <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-check-circle mr-1\"></i>Active
                                        </span>
                                    ";
        } else {
            // line 276
            yield "                                        <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                        </span>
                                    ";
        }
        // line 280
        yield "                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                    Creation Date
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    ";
        // line 290
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 290, $this->source); })()), "createdAt", [], "any", false, false, false, 290), "F j, Y \\a\\t g:i A"), "html", null, true);
        yield "
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/video/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  546 => 290,  534 => 280,  528 => 276,  522 => 272,  520 => 271,  505 => 258,  495 => 250,  488 => 246,  484 => 244,  482 => 243,  476 => 240,  469 => 235,  467 => 234,  457 => 228,  455 => 227,  443 => 217,  433 => 209,  424 => 204,  422 => 203,  411 => 194,  404 => 190,  396 => 184,  394 => 183,  390 => 181,  381 => 177,  373 => 171,  371 => 170,  367 => 168,  360 => 163,  354 => 162,  340 => 151,  330 => 143,  328 => 142,  320 => 136,  316 => 134,  312 => 132,  310 => 131,  307 => 130,  305 => 129,  293 => 119,  289 => 117,  285 => 115,  283 => 114,  280 => 113,  278 => 112,  262 => 99,  246 => 86,  232 => 75,  204 => 50,  181 => 30,  172 => 24,  160 => 14,  147 => 13,  134 => 10,  130 => 9,  125 => 8,  112 => 7,  89 => 5,  66 => 3,  43 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Video Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Video Details{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_video_index') }}\">Videos</a></li>
<li class=\"breadcrumb-item active\">{{ video.title }}</li>
{% endblock %}

{% block content %}
<div class=\"container-fluid\">


    <!-- Integrated Header with Content -->
    <div class=\"card border-0 shadow-lg mb-4\">
        <div class=\"card-header\" style=\"background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;\">
            <div class=\"row align-items-center\">
                <div class=\"col-md-6\">
                    <h2 class=\"card-title mb-0\" style=\"font-size: 1.8rem; font-weight: 600;\">
                        <i class=\"fas fa-video mr-3\" style=\"font-size: 2rem;\"></i>
                        Video Details: {{ video.title }}
                    </h2>
                </div>
                <div class=\"col-md-6\">
                    <div class=\"d-flex justify-content-end align-items-center flex-wrap\">
                        <!-- Edit Video Button (Icon Only) -->
                        <a href=\"{{ path('admin_video_edit', {'slug': video.slug}) }}\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Edit Video\">
                            <i class=\"fas fa-edit\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Print Video Button (Icon Only) -->
                        <a href=\"javascript:void(0)\" onclick=\"window.print()\"
                           class=\"btn me-2 mb-2 mb-md-0\"
                           style=\"border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.querySelector('i').style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.querySelector('i').style.color='#011a2d';\"
                           title=\"Print Video Details\">
                            <i class=\"fas fa-print\" style=\"color: #011a2d;\"></i>
                        </a>

                        <!-- Back to Videos Button -->
                        <a href=\"{{ path('admin_video_index') }}\"
                           class=\"btn mb-2 mb-md-0\"
                           style=\"font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;\"
                           onmouseover=\"this.style.background='#011a2d'; this.style.color='white';\"
                           onmouseout=\"this.style.background='white'; this.style.color='#011a2d';\">
                            <i class=\"fas fa-arrow-left me-2\"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class=\"card-body\">
            <div class=\"row\">
                <div class=\"col-12\">
                    <!-- Video Title and Category (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-8\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-video text-primary mr-1\"></i>
                                    Video Title
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ video.title }}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-4\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-tag text-primary mr-1\"></i>
                                    Category
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ video.category ?? 'Uncategorized' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-align-left text-primary mr-1\"></i>
                            Description
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;\">
                            {{ video.description ?? 'No description provided' }}
                        </div>
                    </div>

                    <!-- Video Source Type and Access Level (same line) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-source text-primary mr-1\"></i>
                                    Video Source Type
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {% if video.videoSourceType == 'youtube' %}
                                        <i class=\"fab fa-youtube text-danger mr-2\"></i>YouTube
                                    {% elseif video.videoSourceType == 'vdocipher' %}
                                        <i class=\"fas fa-shield-alt text-success mr-2\"></i>VdoCipher
                                    {% else %}
                                        <i class=\"fas fa-upload text-info mr-2\"></i>Direct Upload
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-lock text-primary mr-1\"></i>
                                    Access Level
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {% if video.accessLevel == 'public_free' %}
                                        <i class=\"fas fa-globe text-success mr-2\"></i>Public Free
                                    {% elseif video.accessLevel == 'login_required_free' %}
                                        <i class=\"fas fa-user text-warning mr-2\"></i>Login Required Free
                                    {% else %}
                                        <i class=\"fas fa-crown text-danger mr-2\"></i>Premium
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Pricing (if premium) -->
                    {% if video.accessLevel == 'premium' %}
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-dollar-sign text-primary mr-1\"></i>
                                    Price
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 600;\">
                                    \${{ video.price ?? '0.00' }}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-clock text-primary mr-1\"></i>
                                    Access Duration in Days
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                                    {{ video.accessDuration ?? 'Unlimited' }} {% if video.accessDuration %}days{% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- YouTube URL (if YouTube) -->
                    {% if video.videoSourceType == 'youtube' and video.youtubeUrl %}
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fab fa-youtube text-danger mr-1\"></i>
                            YouTube URL
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;\">
                            <a href=\"{{ video.youtubeUrl }}\" target=\"_blank\" class=\"text-decoration-none\">{{ video.youtubeUrl }}</a>
                        </div>
                    </div>
                    {% endif %}

                    <!-- VdoCipher ID (if VdoCipher) -->
                    {% if video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                    <div class=\"form-group mb-4\">
                        <label class=\"form-label\">
                            <i class=\"fas fa-shield-alt text-success mr-1\"></i>
                            VdoCipher ID
                        </label>
                        <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-family: monospace;\">
                            {{ video.vdocipherVideoId }}
                        </div>
                    </div>
                    {% endif %}
                    <!-- Thumbnail and Video (same line, equal height and width) -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-image text-primary mr-1\"></i>
                                    Thumbnail
                                </label>
                                <div class=\"d-flex justify-content-center\">
                                    {% if video.thumbnail %}
                                        <img src=\"{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}\"
                                             alt=\"Video Thumbnail\"
                                             class=\"img-fluid\"
                                             style=\"width: 400px; height: 225px; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                                    {% else %}
                                        <div class=\"d-flex align-items-center justify-content-center\"
                                             style=\"width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                            <div class=\"text-center text-muted\">
                                                <i class=\"fas fa-image\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                <p>No thumbnail available</p>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-play text-primary mr-1\"></i>
                                    Video Player
                                </label>
                                <div class=\"d-flex justify-content-center\">
                                    {% if video.videoSourceType == 'youtube' and video.youtubeEmbedUrl %}
                                        <iframe src=\"{{ video.youtubeEmbedUrl }}\"
                                                style=\"width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\"
                                                frameborder=\"0\"
                                                allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                                allowfullscreen>
                                        </iframe>
                                    {% elseif video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                                        <div class=\"d-flex align-items-center justify-content-center\"
                                             style=\"width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); background: #f8f9fa;\">
                                            <div class=\"text-center\">
                                                <i class=\"fas fa-shield-alt text-success\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                <h5>VdoCipher Protected Video</h5>
                                                <p class=\"text-muted\">ID: {{ video.vdocipherVideoId }}</p>
                                            </div>
                                        </div>
                                    {% elseif video.videoFile %}
                                        <video controls
                                               style=\"width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);\">
                                            <source src=\"{{ asset('uploads/videos/files/' ~ video.videoFile) }}\" type=\"video/mp4\">
                                            Your browser does not support the video tag.
                                        </video>
                                    {% else %}
                                        <div class=\"d-flex align-items-center justify-content-center\"
                                             style=\"width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;\">
                                            <div class=\"text-center text-muted\">
                                                <i class=\"fas fa-video\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                                <p>No video available</p>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Status and Creation Date -->
                    <div class=\"row mb-4\">
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-toggle-on text-primary mr-1\"></i>
                                    Status
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    {% if video.isActive %}
                                        <span class=\"badge bg-success\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-check-circle mr-1\"></i>Active
                                        </span>
                                    {% else %}
                                        <span class=\"badge bg-secondary\" style=\"font-size: 0.9rem; padding: 0.5rem 1rem;\">
                                            <i class=\"fas fa-pause-circle mr-1\"></i>Inactive
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class=\"col-md-6\">
                            <div class=\"form-group\">
                                <label class=\"form-label\">
                                    <i class=\"fas fa-calendar-plus text-primary mr-1\"></i>
                                    Creation Date
                                </label>
                                <div class=\"enhanced-display-field\" style=\"background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 3.5rem; display: flex; align-items: center;\">
                                    {{ video.createdAt|date('F j, Y \\\\a\\\\t g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
", "admin/video/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\video\\show.html.twig");
    }
}
