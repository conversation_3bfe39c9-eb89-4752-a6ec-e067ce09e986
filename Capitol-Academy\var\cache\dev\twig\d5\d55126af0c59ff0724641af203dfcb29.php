<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* cart/widget.html.twig */
class __TwigTemplate_9aa2b8fcde09aefb49d02fdc4c936c18 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->parent = false;

        $this->blocks = [
        ];
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "cart/widget.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "cart/widget.html.twig"));

        // line 1
        yield "<div class=\"cart-widget\">
    <div class=\"cart-icon\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">
        <i class=\"fas fa-shopping-cart\"></i>
        ";
        // line 4
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart_summary"]) || array_key_exists("cart_summary", $context) ? $context["cart_summary"] : (function () { throw new RuntimeError('Variable "cart_summary" does not exist.', 4, $this->source); })()), "item_count", [], "any", false, false, false, 4) > 0)) {
            // line 5
            yield "            <span class=\"cart-badge\">";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart_summary"]) || array_key_exists("cart_summary", $context) ? $context["cart_summary"] : (function () { throw new RuntimeError('Variable "cart_summary" does not exist.', 5, $this->source); })()), "item_count", [], "any", false, false, false, 5), "html", null, true);
            yield "</span>
        ";
        }
        // line 7
        yield "    </div>
    
    <div class=\"dropdown-menu dropdown-menu-end cart-dropdown\" aria-labelledby=\"cartDropdown\">
        <div class=\"cart-dropdown-header\">
            <h6 class=\"mb-0\">
                <i class=\"fas fa-shopping-cart me-2\"></i>Shopping Cart
                ";
        // line 13
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart_summary"]) || array_key_exists("cart_summary", $context) ? $context["cart_summary"] : (function () { throw new RuntimeError('Variable "cart_summary" does not exist.', 13, $this->source); })()), "item_count", [], "any", false, false, false, 13) > 0)) {
            // line 14
            yield "                    (";
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart_summary"]) || array_key_exists("cart_summary", $context) ? $context["cart_summary"] : (function () { throw new RuntimeError('Variable "cart_summary" does not exist.', 14, $this->source); })()), "item_count", [], "any", false, false, false, 14), "html", null, true);
            yield " item";
            yield (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart_summary"]) || array_key_exists("cart_summary", $context) ? $context["cart_summary"] : (function () { throw new RuntimeError('Variable "cart_summary" does not exist.', 14, $this->source); })()), "item_count", [], "any", false, false, false, 14) > 1)) ? ("s") : (""));
            yield ")
                ";
        }
        // line 16
        yield "            </h6>
        </div>
        
        ";
        // line 19
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart_summary"]) || array_key_exists("cart_summary", $context) ? $context["cart_summary"] : (function () { throw new RuntimeError('Variable "cart_summary" does not exist.', 19, $this->source); })()), "is_empty", [], "any", false, false, false, 19)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 20
            yield "            <div class=\"cart-dropdown-empty\">
                <i class=\"fas fa-shopping-cart\"></i>
                <p class=\"mb-2\">Your cart is empty</p>
                <a href=\"";
            // line 23
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_videos_list");
            yield "\" class=\"btn btn-sm btn-primary\">
                    Browse Videos
                </a>
            </div>
        ";
        } else {
            // line 28
            yield "            <div class=\"cart-dropdown-items\">
                ";
            // line 29
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["cart_items"]) || array_key_exists("cart_items", $context) ? $context["cart_items"] : (function () { throw new RuntimeError('Variable "cart_items" does not exist.', 29, $this->source); })()));
            foreach ($context['_seq'] as $context["item_key"] => $context["item"]) {
                // line 30
                yield "                    <div class=\"cart-dropdown-item\">
                        <div class=\"item-thumbnail\">
                            ";
                // line 32
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["item"], "thumbnail", [], "any", false, false, false, 32)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 33
                    yield "                                <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(((("uploads/" . CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 33)) . "s/thumbnails/") . CoreExtension::getAttribute($this->env, $this->source, $context["item"], "thumbnail", [], "any", false, false, false, 33))), "html", null, true);
                    yield "\" 
                                     alt=\"";
                    // line 34
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 34), "html", null, true);
                    yield "\">
                            ";
                } else {
                    // line 36
                    yield "                                <div class=\"placeholder-thumbnail\">
                                    <i class=\"fas fa-play\"></i>
                                </div>
                            ";
                }
                // line 40
                yield "                        </div>
                        <div class=\"item-details\">
                            <h6 class=\"item-title\">";
                // line 42
                yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 42)) > 25)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 42), 0, 25) . "..."), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "title", [], "any", false, false, false, 42), "html", null, true)));
                yield "</h6>
                            <div class=\"item-meta\">
                                <span class=\"item-type\">";
                // line 44
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::titleCase($this->env->getCharset(), Twig\Extension\CoreExtension::replace(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 44), ["_" => " "])), "html", null, true);
                yield "</span>
                                <span class=\"item-price\">\$";
                // line 45
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatNumber(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "price", [], "any", false, false, false, 45), 2), "html", null, true);
                yield "</span>
                            </div>
                        </div>
                        <button class=\"remove-item-btn\" onclick=\"removeFromCart('";
                // line 48
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "type", [], "any", false, false, false, 48), "html", null, true);
                yield "', ";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["item"], "id", [], "any", false, false, false, 48), "html", null, true);
                yield ")\" title=\"Remove item\">
                            <i class=\"fas fa-times\"></i>
                        </button>
                    </div>
                ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['item_key'], $context['item'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 53
            yield "                
                ";
            // line 54
            if (((isset($context["total_items"]) || array_key_exists("total_items", $context) ? $context["total_items"] : (function () { throw new RuntimeError('Variable "total_items" does not exist.', 54, $this->source); })()) > 3)) {
                // line 55
                yield "                    <div class=\"cart-dropdown-more\">
                        <small class=\"text-muted\">
                            +";
                // line 57
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(((isset($context["total_items"]) || array_key_exists("total_items", $context) ? $context["total_items"] : (function () { throw new RuntimeError('Variable "total_items" does not exist.', 57, $this->source); })()) - 3), "html", null, true);
                yield " more item";
                yield (((((isset($context["total_items"]) || array_key_exists("total_items", $context) ? $context["total_items"] : (function () { throw new RuntimeError('Variable "total_items" does not exist.', 57, $this->source); })()) - 3) > 1)) ? ("s") : (""));
                yield "
                        </small>
                    </div>
                ";
            }
            // line 61
            yield "            </div>
            
            <div class=\"cart-dropdown-footer\">
                <div class=\"cart-total\">
                    <strong>Total: \$";
            // line 65
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["cart_summary"]) || array_key_exists("cart_summary", $context) ? $context["cart_summary"] : (function () { throw new RuntimeError('Variable "cart_summary" does not exist.', 65, $this->source); })()), "total", [], "any", false, false, false, 65), "html", null, true);
            yield "</strong>
                </div>
                <div class=\"cart-actions\">
                    <a href=\"";
            // line 68
            yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart");
            yield "\" class=\"btn btn-outline-primary btn-sm\">
                        View Cart
                    </a>
                    ";
            // line 71
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 71, $this->source); })()), "user", [], "any", false, false, false, 71)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 72
                yield "                        <button class=\"btn btn-primary btn-sm\" onclick=\"alert('Checkout functionality coming soon')\">
                            Checkout
                        </button>
                    ";
            } else {
                // line 76
                yield "                        <a href=\"";
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
                yield "\" class=\"btn btn-primary btn-sm\">
                            Login
                        </a>
                    ";
            }
            // line 80
            yield "                </div>
            </div>
        ";
        }
        // line 83
        yield "    </div>
</div>

<style>
.cart-widget {
    position: relative;
}

.cart-icon {
    position: relative;
    cursor: pointer;
    padding: 8px;
    color: white;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.cart-icon:hover {
    color: #a90418;
}

.cart-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #a90418;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.cart-dropdown {
    width: 350px;
    max-height: 500px;
    overflow-y: auto;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 10px;
}

.cart-dropdown-header {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.cart-dropdown-empty {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.cart-dropdown-empty i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #dee2e6;
}

.cart-dropdown-items {
    max-height: 300px;
    overflow-y: auto;
}

.cart-dropdown-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f8f9fa;
    transition: background 0.3s ease;
}

.cart-dropdown-item:hover {
    background: #f8f9fa;
}

.item-thumbnail {
    width: 50px;
    height: 40px;
    border-radius: 5px;
    overflow: hidden;
    margin-right: 12px;
    flex-shrink: 0;
}

.item-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-thumbnail {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.item-details {
    flex: 1;
    min-width: 0;
}

.item-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #011a2d;
    margin-bottom: 4px;
    line-height: 1.2;
}

.item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.item-type {
    background: #e9ecef;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    font-size: 0.7rem;
}

.item-price {
    font-weight: 600;
    color: #a90418;
}

.remove-item-btn {
    background: none;
    border: none;
    color: #dc3545;
    padding: 5px;
    margin-left: 10px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.remove-item-btn:hover {
    opacity: 1;
}

.cart-dropdown-more {
    padding: 10px 20px;
    text-align: center;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.cart-dropdown-footer {
    padding: 15px 20px;
    background: #f8f9fa;
}

.cart-total {
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: #011a2d;
}

.cart-actions {
    display: flex;
    gap: 10px;
}

.cart-actions .btn {
    flex: 1;
    font-size: 0.85rem;
}

@media (max-width: 768px) {
    .cart-dropdown {
        width: 300px;
    }
    
    .cart-dropdown-item {
        padding: 12px 15px;
    }
    
    .item-thumbnail {
        width: 40px;
        height: 32px;
    }
}
</style>

<script>
function removeFromCart(type, id) {
    fetch('";
        // line 286
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart_remove");
        yield "', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=\${type}&id=\${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the cart widget
            updateCartWidget();
        } else {
            alert(data.message || 'Failed to remove item');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while removing the item');
    });
}

function updateCartWidget() {
    fetch('";
        // line 309
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart_widget");
        yield "')
    .then(response => response.text())
    .then(html => {
        document.querySelector('.cart-widget').outerHTML = html;
    })
    .catch(error => {
        console.error('Error updating cart widget:', error);
    });
}
</script>
";
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "cart/widget.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  448 => 309,  422 => 286,  217 => 83,  212 => 80,  204 => 76,  198 => 72,  196 => 71,  190 => 68,  184 => 65,  178 => 61,  169 => 57,  165 => 55,  163 => 54,  160 => 53,  147 => 48,  141 => 45,  137 => 44,  132 => 42,  128 => 40,  122 => 36,  117 => 34,  112 => 33,  110 => 32,  106 => 30,  102 => 29,  99 => 28,  91 => 23,  86 => 20,  84 => 19,  79 => 16,  71 => 14,  69 => 13,  61 => 7,  55 => 5,  53 => 4,  48 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("<div class=\"cart-widget\">
    <div class=\"cart-icon\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">
        <i class=\"fas fa-shopping-cart\"></i>
        {% if cart_summary.item_count > 0 %}
            <span class=\"cart-badge\">{{ cart_summary.item_count }}</span>
        {% endif %}
    </div>
    
    <div class=\"dropdown-menu dropdown-menu-end cart-dropdown\" aria-labelledby=\"cartDropdown\">
        <div class=\"cart-dropdown-header\">
            <h6 class=\"mb-0\">
                <i class=\"fas fa-shopping-cart me-2\"></i>Shopping Cart
                {% if cart_summary.item_count > 0 %}
                    ({{ cart_summary.item_count }} item{{ cart_summary.item_count > 1 ? 's' : '' }})
                {% endif %}
            </h6>
        </div>
        
        {% if cart_summary.is_empty %}
            <div class=\"cart-dropdown-empty\">
                <i class=\"fas fa-shopping-cart\"></i>
                <p class=\"mb-2\">Your cart is empty</p>
                <a href=\"{{ path('app_videos_list') }}\" class=\"btn btn-sm btn-primary\">
                    Browse Videos
                </a>
            </div>
        {% else %}
            <div class=\"cart-dropdown-items\">
                {% for item_key, item in cart_items %}
                    <div class=\"cart-dropdown-item\">
                        <div class=\"item-thumbnail\">
                            {% if item.thumbnail %}
                                <img src=\"{{ asset('uploads/' ~ item.type ~ 's/thumbnails/' ~ item.thumbnail) }}\" 
                                     alt=\"{{ item.title }}\">
                            {% else %}
                                <div class=\"placeholder-thumbnail\">
                                    <i class=\"fas fa-play\"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class=\"item-details\">
                            <h6 class=\"item-title\">{{ item.title|length > 25 ? item.title|slice(0, 25) ~ '...' : item.title }}</h6>
                            <div class=\"item-meta\">
                                <span class=\"item-type\">{{ item.type|replace({'_': ' '})|title }}</span>
                                <span class=\"item-price\">\${{ item.price|number_format(2) }}</span>
                            </div>
                        </div>
                        <button class=\"remove-item-btn\" onclick=\"removeFromCart('{{ item.type }}', {{ item.id }})\" title=\"Remove item\">
                            <i class=\"fas fa-times\"></i>
                        </button>
                    </div>
                {% endfor %}
                
                {% if total_items > 3 %}
                    <div class=\"cart-dropdown-more\">
                        <small class=\"text-muted\">
                            +{{ total_items - 3 }} more item{{ total_items - 3 > 1 ? 's' : '' }}
                        </small>
                    </div>
                {% endif %}
            </div>
            
            <div class=\"cart-dropdown-footer\">
                <div class=\"cart-total\">
                    <strong>Total: \${{ cart_summary.total }}</strong>
                </div>
                <div class=\"cart-actions\">
                    <a href=\"{{ path('app_cart') }}\" class=\"btn btn-outline-primary btn-sm\">
                        View Cart
                    </a>
                    {% if app.user %}
                        <button class=\"btn btn-primary btn-sm\" onclick=\"alert('Checkout functionality coming soon')\">
                            Checkout
                        </button>
                    {% else %}
                        <a href=\"{{ path('app_login') }}\" class=\"btn btn-primary btn-sm\">
                            Login
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>

<style>
.cart-widget {
    position: relative;
}

.cart-icon {
    position: relative;
    cursor: pointer;
    padding: 8px;
    color: white;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.cart-icon:hover {
    color: #a90418;
}

.cart-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #a90418;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.cart-dropdown {
    width: 350px;
    max-height: 500px;
    overflow-y: auto;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 10px;
}

.cart-dropdown-header {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.cart-dropdown-empty {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.cart-dropdown-empty i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #dee2e6;
}

.cart-dropdown-items {
    max-height: 300px;
    overflow-y: auto;
}

.cart-dropdown-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f8f9fa;
    transition: background 0.3s ease;
}

.cart-dropdown-item:hover {
    background: #f8f9fa;
}

.item-thumbnail {
    width: 50px;
    height: 40px;
    border-radius: 5px;
    overflow: hidden;
    margin-right: 12px;
    flex-shrink: 0;
}

.item-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-thumbnail {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #011a2d 0%, #a90418 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.item-details {
    flex: 1;
    min-width: 0;
}

.item-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #011a2d;
    margin-bottom: 4px;
    line-height: 1.2;
}

.item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.item-type {
    background: #e9ecef;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    font-size: 0.7rem;
}

.item-price {
    font-weight: 600;
    color: #a90418;
}

.remove-item-btn {
    background: none;
    border: none;
    color: #dc3545;
    padding: 5px;
    margin-left: 10px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.remove-item-btn:hover {
    opacity: 1;
}

.cart-dropdown-more {
    padding: 10px 20px;
    text-align: center;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.cart-dropdown-footer {
    padding: 15px 20px;
    background: #f8f9fa;
}

.cart-total {
    text-align: center;
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: #011a2d;
}

.cart-actions {
    display: flex;
    gap: 10px;
}

.cart-actions .btn {
    flex: 1;
    font-size: 0.85rem;
}

@media (max-width: 768px) {
    .cart-dropdown {
        width: 300px;
    }
    
    .cart-dropdown-item {
        padding: 12px 15px;
    }
    
    .item-thumbnail {
        width: 40px;
        height: 32px;
    }
}
</style>

<script>
function removeFromCart(type, id) {
    fetch('{{ path('app_cart_remove') }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `type=\${type}&id=\${id}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the cart widget
            updateCartWidget();
        } else {
            alert(data.message || 'Failed to remove item');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while removing the item');
    });
}

function updateCartWidget() {
    fetch('{{ path('app_cart_widget') }}')
    .then(response => response.text())
    .then(html => {
        document.querySelector('.cart-widget').outerHTML = html;
    })
    .catch(error => {
        console.error('Error updating cart widget:', error);
    });
}
</script>
", "cart/widget.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\cart\\widget.html.twig");
    }
}
