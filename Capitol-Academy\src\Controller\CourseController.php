<?php

namespace App\Controller;

use App\Repository\CourseModuleRepository;
use App\Repository\CourseRepository;
use App\Service\CartService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/courses')]
class CourseController extends AbstractController
{
    #[Route('/', name: 'app_courses_list')]
    #[Route('/', name: 'app_courses')]
    public function list(CourseRepository $courseRepository): Response
    {
        $courses = $courseRepository->findActiveCourses();

        return $this->render('course/list.html.twig', [
            'courses' => $courses,
        ]);
    }

    #[Route('/{code}', name: 'app_course_show')]
    public function show(string $code, CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        $course = $courseRepository->findByCode($code);

        if (!$course) {
            throw $this->createNotFoundException('Course not found');
        }

        // Increment view count
        $course->incrementViewCount();
        $entityManager->flush();

        $modules = $moduleRepository->findByCourseOrdered($course);

        // Since we simplified the course system, no enrollment check needed
        $isEnrolled = false;
        $user = $this->getUser();

        return $this->render('course/show.html.twig', [
            'course' => $course,
            'modules' => $modules,
            'is_enrolled' => $isEnrolled,
            'user' => $user,
        ]);
    }

    #[Route('/{courseCode}/{moduleCode}', name: 'app_course_module_show')]
    public function showModule(
        string $courseCode,
        string $moduleCode,
        CourseRepository $courseRepository,
        CourseModuleRepository $moduleRepository
    ): Response {
        $course = $courseRepository->findByCode($courseCode);

        if (!$course) {
            throw $this->createNotFoundException('Course not found');
        }

        $module = $moduleRepository->findByCode($moduleCode);

        if (!$module || $module->getCourse() !== $course) {
            throw $this->createNotFoundException('Module not found');
        }

        $allModules = $moduleRepository->findByCourseOrdered($course);
        
        return $this->render('course/module.html.twig', [
            'course' => $course,
            'module' => $module,
            'all_modules' => $allModules,
        ]);
    }

    // Individual course routes for legacy compatibility
    #[Route('/financial-markets', name: 'app_course_fma')]
    public function financialMarkets(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('FMA', $courseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/technical-analysis', name: 'app_course_tec')]
    public function technicalAnalysis(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('TEC', $courseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/trading-strategies', name: 'app_course_trs')]
    public function tradingStrategies(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('TRS', $courseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/fundamental-analysis', name: 'app_course_fun')]
    public function fundamentalAnalysis(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('FUN', $courseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/psychological-analysis', name: 'app_course_ssa')]
    public function psychologicalAnalysis(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('SSA', $courseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/capital-management', name: 'app_course_mma')]
    public function capitalManagement(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('MMA', $courseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/risk-management', name: 'app_course_rsk')]
    public function riskManagement(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('RSK', $courseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/day-trading', name: 'app_course_dtr')]
    public function dayTrading(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('DTR', $courseRepository, $moduleRepository, $entityManager);
    }

    #[Route('/professional-trader', name: 'app_course_pro')]
    public function professionalTrader(CourseRepository $courseRepository, CourseModuleRepository $moduleRepository, EntityManagerInterface $entityManager): Response
    {
        return $this->show('PTR', $courseRepository, $moduleRepository, $entityManager);
    }



    #[Route('/add-to-cart/{code}', name: 'app_course_add_to_cart', methods: ['POST'])]
    public function addToCart(string $code, CourseRepository $courseRepository, CartService $cartService): JsonResponse
    {
        $course = $courseRepository->findByCode($code);

        if (!$course) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Course not found'
            ], 404);
        }

        if (!$course->isActive()) {
            return new JsonResponse([
                'success' => false,
                'message' => 'This course is not available'
            ], 400);
        }

        // Check if already in cart - silently ignore duplicates
        if ($cartService->hasItem('course', $course->getId())) {
            $cartSummary = $cartService->getCartSummary();

            return new JsonResponse([
                'success' => true,
                'message' => 'Course is already in your cart',
                'cart' => $cartSummary,
                'duplicate' => true
            ]);
        }

        $success = $cartService->addItem('course', $course->getId(), 1);

        if ($success) {
            return new JsonResponse([
                'success' => true,
                'message' => 'Course added to cart successfully',
                'cart' => $cartService->getCartSummary()
            ]);
        }

        return new JsonResponse([
            'success' => false,
            'message' => 'Failed to add course to cart'
        ], 500);
    }


}
