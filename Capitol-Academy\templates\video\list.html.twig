{% extends 'base.html.twig' %}

{% block title %}Video Library - Capitol Academy{% endblock %}

{% block meta_description %}Explore our comprehensive video library featuring professional trading education content, market analysis, and expert insights from Capitol Academy.{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Video Library Styles */
        .video-library-hero {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            padding: 120px 0 80px;
            position: relative;
        }

        .hero-content {
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            font-family: 'Montserrat', sans-serif;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 0;
            opacity: 0.9;
            font-family: '<PERSON><PERSON>ri', Arial, sans-serif;
        }

        .search-section {
            background: white;
            padding: 40px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .search-container {
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }

        .search-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            padding: 15px 50px 15px 25px;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #011a2d;
            background: white;
            box-shadow: 0 0 0 3px rgba(1, 26, 45, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
        }

        .filter-section {
            background: #f8f9fa;
            padding: 30px 0;
        }

        .filter-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .filter-btn {
            background: white;
            color: #6c757d;
            border: 2px solid #e9ecef;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #011a2d;
            border-color: #011a2d;
            color: white;
            transform: translateY(-1px);
        }

        .video-catalog-section {
            padding: 60px 0;
            background: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-title h2 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 10px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: #6c757d;
            margin: 0;
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .video-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.1);
            transition: all 0.3s ease;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid #f1f3f4;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.15);
            border-color: #e9ecef;
        }

        .video-thumbnail {
            position: relative;
            height: 180px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .video-card:hover .video-thumbnail img {
            transform: scale(1.03);
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(1, 26, 45, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-card:hover .video-overlay {
            opacity: 1;
        }

        .play-button {
            width: 50px;
            height: 50px;
            background: #a90418;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: #8b0314;
            transform: scale(1.1);
        }

        .video-badges {
            position: absolute;
            top: 12px;
            left: 12px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .video-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .badge-free {
            background: rgba(40, 167, 69, 0.95);
            color: white;
        }

        .badge-premium {
            background: rgba(169, 4, 24, 0.95);
            color: white;
        }

        .badge-login-required {
            background: rgba(1, 26, 45, 0.95);
            color: white;
        }

        .video-content {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .video-title {
            color: #011a2d;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 12px;
            line-height: 1.4;
            font-family: 'Montserrat', sans-serif;
            font-family: 'Montserrat', sans-serif;
        }

        .video-description {
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .video-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .video-category {
            background: #e9ecef;
            color: #495057;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: 600;
        }

        .video-date {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .video-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn-watch {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-watch:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .btn-purchase {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-purchase:hover {
            background: linear-gradient(135deg, #e0a800, #ffc107);
            transform: translateY(-2px);
            color: #000;
            text-decoration: none;
        }

        .btn-login {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-login:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .btn-details {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
            text-decoration: none;
        }

        .btn-details:hover {
            background: linear-gradient(135deg, #138496, #17a2b8);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .no-videos {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-videos i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .loading-spinner {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #a90418;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .video-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .filter-buttons {
                justify-content: flex-start;
            }

            .search-filter-section {
                padding: 20px;
            }
        }
    </style>
{% endblock %}

{% block body %}
<!-- Video Library Hero Section -->
<section class="video-library-hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Video Library</h1>
            <p class="hero-subtitle">
                Explore our comprehensive collection of professional trading education videos,
                market analysis, and expert insights to enhance your trading skills.
            </p>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="search-section">
    <div class="container">
        <div class="search-container">
            <input type="text"
                   class="search-box"
                   id="video-search"
                   placeholder="Search videos by title, description, or category...">
            <i class="fas fa-search search-icon"></i>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="filter-section">
    <div class="container">
        <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">All Videos</button>
            <button class="filter-btn" data-filter="free">Free</button>
            <button class="filter-btn" data-filter="premium">Premium</button>
        </div>
    </div>
</section>
<!-- Video Catalog Section -->
<section class="video-catalog-section">
    <div class="container">
        <div class="section-title">
            <h2>Our Video Collection</h2>
            <p>Professional trading education content from industry experts</p>
        </div>
                <!-- Loading Spinner -->
                <div class="loading-spinner" id="loading-spinner" style="display: none;">
                    <div class="spinner"></div>
                    <p>Loading videos...</p>
                </div>

                <!-- Video Grid -->
                <div class="video-grid" id="video-grid">
                    {% if videos|length > 0 %}
                        {% for video in videos %}
                            <div class="video-card"
                                 data-category="{{ video.category }}"
                                 data-access-level="{{ video.accessLevel }}"
                                 data-title="{{ video.title|lower }}"
                                 data-description="{{ video.description|lower }}">

                                <!-- Video Thumbnail -->
                                <div class="video-thumbnail">
                                    {% if video.thumbnail %}
                                        <img src="{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}"
                                             alt="{{ video.title }}"
                                             loading="lazy">
                                    {% else %}
                                        <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #f8f9fa, #e9ecef); display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-video text-muted" style="font-size: 3rem;"></i>
                                        </div>
                                    {% endif %}

                                    <!-- Video Overlay -->
                                    <div class="video-overlay">
                                        <div class="play-button">
                                            <i class="fas fa-play"></i>
                                        </div>
                                    </div>

                                    <!-- Video Badges -->
                                    <div class="video-badges">
                                        <!-- Access Level Badge -->
                                        {% if video.accessLevel == 'public_free' %}
                                            <span class="video-badge badge-free">
                                                <i class="fas fa-globe"></i> Free
                                            </span>
                                        {% elseif video.accessLevel == 'login_required_free' %}
                                            <span class="video-badge badge-login-required">
                                                <i class="fas fa-user"></i> Login Required
                                            </span>
                                        {% else %}
                                            <span class="video-badge badge-premium">
                                                <i class="fas fa-crown"></i> {{ video.formattedPrice }}
                                            </span>
                                        {% endif %}


                                    </div>
                                </div>

                                <!-- Video Content -->
                                <div class="video-content">
                                    <h3 class="video-title">{{ video.title }}</h3>

                                    <!-- Video Meta -->
                                    <div class="video-meta">
                                        {% if video.category %}
                                            <span class="video-category">{{ video.category }}</span>
                                        {% endif %}
                                        <span class="video-date">
                                            <i class="fas fa-calendar"></i>
                                            {{ video.createdAt|date('M d, Y') }}
                                        </span>
                                    </div>

                                    <!-- Video Description -->
                                    {% if video.description %}
                                        <p class="video-description">
                                            {{ video.description|length > 120 ? video.description|slice(0, 120) ~ '...' : video.description }}
                                        </p>
                                    {% endif %}

                                    <!-- Video Actions -->
                                    <div class="video-actions">
                                        <a href="{{ path('app_video_show', {'slug': video.slug}) }}" class="btn-details">
                                            <i class="fas fa-info-circle"></i> See Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-videos">
                            <i class="fas fa-video"></i>
                            <h3>No Videos Available</h3>
                            <p>We're working on adding more content. Please check back soon!</p>
                        </div>
                    {% endif %}
                </div>

                <!-- No Results Message -->
                <div class="no-videos" id="no-results" style="display: none;">
                    <i class="fas fa-search"></i>
                    <h3>No Videos Found</h3>
                    <p>Try adjusting your search terms or filters to find what you're looking for.</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('video-search');
            const filterButtons = document.querySelectorAll('.filter-btn');
            const videoCards = document.querySelectorAll('.video-card');
            const videoGrid = document.getElementById('video-grid');
            const noResults = document.getElementById('no-results');
            const loadingSpinner = document.getElementById('loading-spinner');

            let currentFilter = 'all';
            let currentSearch = '';

            // Search functionality
            searchInput.addEventListener('input', function() {
                currentSearch = this.value.toLowerCase().trim();
                filterVideos();
            });

            // Filter functionality
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Update active filter button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    currentFilter = this.dataset.filter;
                    filterVideos();
                });
            });

            // Filter videos based on search and category
            function filterVideos() {
                showLoading();

                setTimeout(() => {
                    let visibleCount = 0;

                    videoCards.forEach(card => {
                        const title = card.dataset.title || '';
                        const description = card.dataset.description || '';
                        const category = card.dataset.category || '';
                        const accessLevel = card.dataset.accessLevel || '';

                        // Check search match
                        const searchMatch = currentSearch === '' ||
                            title.includes(currentSearch) ||
                            description.includes(currentSearch) ||
                            category.toLowerCase().includes(currentSearch);

                        // Check filter match
                        let filterMatch = true;
                        if (currentFilter !== 'all') {
                            if (['public_free', 'login_required_free', 'premium'].includes(currentFilter)) {
                                filterMatch = accessLevel === currentFilter;
                            } else {
                                filterMatch = category === currentFilter;
                            }
                        }

                        // Show/hide card
                        if (searchMatch && filterMatch) {
                            card.style.display = 'flex';
                            visibleCount++;
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    // Show/hide no results message
                    if (visibleCount === 0) {
                        noResults.style.display = 'block';
                        videoGrid.style.display = 'none';
                    } else {
                        noResults.style.display = 'none';
                        videoGrid.style.display = 'grid';
                    }

                    hideLoading();
                }, 300); // Small delay for better UX
            }

            function showLoading() {
                loadingSpinner.style.display = 'block';
                videoGrid.style.opacity = '0.5';
            }

            function hideLoading() {
                loadingSpinner.style.display = 'none';
                videoGrid.style.opacity = '1';
            }

            // Video card click handlers
            videoCards.forEach(card => {
                const thumbnail = card.querySelector('.video-thumbnail');
                const playButton = card.querySelector('.play-button');

                if (thumbnail && playButton) {
                    thumbnail.addEventListener('click', function() {
                        const detailsButton = card.querySelector('.btn-details');

                        if (detailsButton) {
                            window.location.href = detailsButton.href;
                        }
                    });
                }
            });

            // Smooth animations for filter changes
            function animateCards() {
                videoCards.forEach((card, index) => {
                    if (card.style.display !== 'none') {
                        card.style.animation = `fadeInUp 0.6s ease forwards ${index * 0.1}s`;
                    }
                });
            }

            // Add CSS animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);

            // Initial animation
            animateCards();

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Focus search on Ctrl+F or Cmd+F
                if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                    e.preventDefault();
                    searchInput.focus();
                }

                // Clear search on Escape
                if (e.key === 'Escape' && document.activeElement === searchInput) {
                    searchInput.value = '';
                    currentSearch = '';
                    filterVideos();
                }
            });

            // URL hash handling for direct links to filtered content
            function handleHashChange() {
                const hash = window.location.hash.substring(1);
                if (hash) {
                    const filterButton = document.querySelector(`[data-filter="${hash}"]`);
                    if (filterButton) {
                        filterButton.click();
                    }
                }
            }

            // Handle initial hash and hash changes
            handleHashChange();
            window.addEventListener('hashchange', handleHashChange);

            // Update URL hash when filter changes
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.dataset.filter;
                    if (filter !== 'all') {
                        window.history.replaceState(null, null, `#${filter}`);
                    } else {
                        window.history.replaceState(null, null, window.location.pathname);
                    }
                });
            });

            // Performance optimization: Debounce search
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // Replace immediate search with debounced version
            const debouncedFilter = debounce(filterVideos, 300);
            searchInput.removeEventListener('input', filterVideos);
            searchInput.addEventListener('input', function() {
                currentSearch = this.value.toLowerCase().trim();
                debouncedFilter();
            });
        });
    </script>
{% endblock %}