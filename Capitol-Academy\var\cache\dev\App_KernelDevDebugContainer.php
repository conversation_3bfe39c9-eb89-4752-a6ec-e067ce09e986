<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerJeqsF8h\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerJeqsF8h/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerJeqsF8h.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerJeqsF8h\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerJeqsF8h\App_KernelDevDebugContainer([
    'container.build_hash' => 'JeqsF8h',
    'container.build_id' => 'e1e2bb99',
    'container.build_time' => 1752853833,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerJeqsF8h');
