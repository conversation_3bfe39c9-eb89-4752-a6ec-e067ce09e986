<?php

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.

if (\class_exists(\ContainerTnKUPFC\App_KernelDevDebugContainer::class, false)) {
    // no-op
} elseif (!include __DIR__.'/ContainerTnKUPFC/App_KernelDevDebugContainer.php') {
    touch(__DIR__.'/ContainerTnKUPFC.legacy');

    return;
}

if (!\class_exists(App_KernelDevDebugContainer::class, false)) {
    \class_alias(\ContainerTnKUPFC\App_KernelDevDebugContainer::class, App_KernelDevDebugContainer::class, false);
}

return new \ContainerTnKUPFC\App_KernelDevDebugContainer([
    'container.build_hash' => 'TnKUPFC',
    'container.build_id' => '977aa102',
    'container.build_time' => 1752843473,
    'container.runtime_mode' => \in_array(\PHP_SAPI, ['cli', 'phpdbg', 'embed'], true) ? 'web=0' : 'web=1',
], __DIR__.\DIRECTORY_SEPARATOR.'ContainerTnKUPFC');
