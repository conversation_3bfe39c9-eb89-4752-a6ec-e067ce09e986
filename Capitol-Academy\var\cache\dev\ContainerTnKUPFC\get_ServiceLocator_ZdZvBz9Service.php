<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_ZdZvBz9Service extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.ZdZvBz9' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.ZdZvBz9'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'courseRepository' => ['privates', 'App\\Repository\\CourseRepository', 'getCourseRepositoryService', true],
            'entityManager' => ['services', 'doctrine.orm.default_entity_manager', 'getDoctrine_Orm_DefaultEntityManagerService', false],
            'moduleRepository' => ['privates', 'App\\Repository\\CourseModuleRepository', 'getCourseModuleRepositoryService', true],
        ], [
            'courseRepository' => 'App\\Repository\\CourseRepository',
            'entityManager' => '?',
            'moduleRepository' => 'App\\Repository\\CourseModuleRepository',
        ]);
    }
}
