<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* admin/video/index.html.twig */
class __TwigTemplate_2b3e38b456dd2d5f7681233ce40ad52c extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'page_title' => [$this, 'block_page_title'],
            'breadcrumbs' => [$this, 'block_breadcrumbs'],
            'content' => [$this, 'block_content'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "admin/base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/index.html.twig"));

        $this->parent = $this->load("admin/base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Video Management - Capitol Academy Admin";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_page_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "page_title"));

        yield "Video Management";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_breadcrumbs(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "breadcrumbs"));

        // line 8
        yield "<li class=\"breadcrumb-item\"><a href=\"";
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_dashboard");
        yield "\">Home</a></li>
<li class=\"breadcrumb-item active\">Videos</li>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 12
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "content"));

        // line 13
        $context["page_config"] = ["page_title" => "Video Management", "page_icon" => "fas fa-video", "search_placeholder" => "Search videos by title, category, or description...", "create_button" => ["url" => $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_new"), "text" => "Add New Video", "icon" => "fas fa-plus"], "stats" => [["title" => "Total Videos", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(),         // line 25
(isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 25, $this->source); })())), "icon" => "fas fa-video", "color" => "#1e3c72", "gradient" => "linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)"], ["title" => "Free Videos", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 32
(isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 32, $this->source); })()), function ($__video__) use ($context, $macros) { $context["video"] = $__video__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 32, $this->source); })()), "isFree", [], "any", false, false, false, 32); })), "icon" => "fas fa-unlock", "color" => "#28a745", "gradient" => "linear-gradient(135deg, #28a745 0%, #20c997 100%)"], ["title" => "Premium Videos", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 39
(isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 39, $this->source); })()), function ($__video__) use ($context, $macros) { $context["video"] = $__video__; return  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 39, $this->source); })()), "isFree", [], "any", false, false, false, 39); })), "icon" => "fas fa-crown", "color" => "#ffc107", "gradient" => "linear-gradient(135deg, #ffc107 0%, #e0a800 100%)"], ["title" => "Active Videos", "value" => Twig\Extension\CoreExtension::length($this->env->getCharset(), Twig\Extension\CoreExtension::filter($this->env,         // line 46
(isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 46, $this->source); })()), function ($__video__) use ($context, $macros) { $context["video"] = $__video__; return CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 46, $this->source); })()), "isActive", [], "any", false, false, false, 46); })), "icon" => "fas fa-play-circle", "color" => "#a90418", "gradient" => "linear-gradient(135deg, #a90418 0%, #8b0314 100%)"]]];
        // line 53
        yield "
";
        // line 54
        yield from $this->load("admin/video/index.html.twig", 54, "1856916277")->unwrap()->yield(CoreExtension::merge($context, (isset($context["page_config"]) || array_key_exists("page_config", $context) ? $context["page_config"] : (function () { throw new RuntimeError('Variable "page_config" does not exist.', 54, $this->source); })())));
        // line 128
        yield "

";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 132
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 133
        yield "<script>
// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Video management functions
function toggleVideoStatus(videoSlug, videoTitle, currentStatus) {
    showStatusModal(videoTitle, currentStatus, function() {
        executeVideoStatusToggle(videoSlug);
    });
}

function deleteVideo(videoSlug, videoTitle) {
    showDeleteModal(videoTitle, function() {
        executeVideoDelete(videoSlug);
    });
}

// Actual execution functions
function executeVideoStatusToggle(videoSlug) {
    fetch(`/admin/videos/\${videoSlug}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the video status');
    });
}

function executeVideoDelete(videoSlug) {
    fetch(`/admin/videos/\${videoSlug}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the video');
    });
}
</script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/video/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  191 => 133,  178 => 132,  165 => 128,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Video Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Video Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Videos</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Video Management',
    'page_icon': 'fas fa-video',
    'search_placeholder': 'Search videos by title, category, or description...',
    'create_button': {
        'url': path('admin_video_new'),
        'text': 'Add New Video',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Videos',
            'value': videos|length,
            'icon': 'fas fa-video',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Free Videos',
            'value': videos|filter(video => video.isFree)|length,
            'icon': 'fas fa-unlock',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Premium Videos',
            'value': videos|filter(video => not video.isFree)|length,
            'icon': 'fas fa-crown',
            'color': '#ffc107',
            'gradient': 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)'
        },
        {
            'title': 'Active Videos',
            'value': videos|filter(video => video.isActive)|length,
            'icon': 'fas fa-play-circle',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Thumbnail'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Source'},
            {'text': 'Access Level'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for video in videos %}
            {% set row_cells = [
                {
                    'content': video.thumbnail ?
                        '<img src=\"/uploads/videos/thumbnails/' ~ video.thumbnail ~ '\" alt=\"' ~ video.title ~ '\" class=\"img-thumbnail\" style=\"width: 60px; height: 40px; object-fit: cover;\">' :
                        '<div class=\"bg-light d-flex align-items-center justify-content-center\" style=\"width: 60px; height: 40px; border-radius: 4px;\"><i class=\"fas fa-video text-muted\"></i></div>'
                },
                {
                    'content': '<h6 class=\"video-title mb-0 font-weight-bold text-dark\">' ~ video.title ~ '</h6>'
                },
                {
                    'content': video.category ?
                        '<span class=\"badge bg-info\">' ~ video.category ~ '</span>' :
                        '<span class=\"text-muted\">No category</span>'
                },
                {
                    'content':
                        (video.videoSourceType == 'youtube') ? '<span class=\"badge bg-danger\"><i class=\"fab fa-youtube mr-1\"></i>YouTube</span>' :
                        (video.videoSourceType == 'vdocipher') ? '<span class=\"badge bg-success\"><i class=\"fas fa-shield-alt mr-1\"></i>VdoCipher</span>' :
                        '<span class=\"badge bg-primary\"><i class=\"fas fa-upload mr-1\"></i>Upload</span>'
                },
                {
                    'content':
                        (video.accessLevel == 'public_free') ? '<span class=\"badge bg-success\"><i class=\"fas fa-globe mr-1\"></i>Public Free</span>' :
                        (video.accessLevel == 'login_required_free') ? '<span class=\"badge bg-info\"><i class=\"fas fa-user mr-1\"></i>Login Required</span>' :
                        '<span class=\"badge bg-warning\"><i class=\"fas fa-crown mr-1\"></i>Premium</span>'
                },
                {
                    'content': video.formattedPrice
                },
                {
                    'content': '<span class=\"badge ' ~ (video.isActive ? 'bg-success' : 'bg-secondary') ~ '\">' ~ (video.isActive ? 'Active' : 'Inactive') ~ '</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_video_show', {'id': video.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Video\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_video_edit', {'id': video.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Video\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ' ~ (video.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (video.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (video.isActive ? 'Deactivate' : 'Activate') ~ ' Video\" onclick=\"toggleVideoStatus(\\'' ~ video.slug ~ '\\', \\'' ~ video.title|e('js') ~ '\\', ' ~ (video.isActive ? 'true' : 'false') ~ ')\"><i class=\"fas fa-' ~ (video.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Video\" onclick=\"deleteVideo(\\'' ~ video.slug ~ '\\', \\'' ~ video.title|e('js') ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'video-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'video-row',
            'empty_message': 'No videos found',
            'empty_icon': 'fas fa-video',
            'empty_description': 'Get started by creating your first video.',
            'search_config': {
                'fields': ['.video-title']
            }
        } %}
    {% endblock %}
{% endembed %}


{% endblock %}

{% block javascripts %}
<script>
// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Video management functions
function toggleVideoStatus(videoSlug, videoTitle, currentStatus) {
    showStatusModal(videoTitle, currentStatus, function() {
        executeVideoStatusToggle(videoSlug);
    });
}

function deleteVideo(videoSlug, videoTitle) {
    showDeleteModal(videoTitle, function() {
        executeVideoDelete(videoSlug);
    });
}

// Actual execution functions
function executeVideoStatusToggle(videoSlug) {
    fetch(`/admin/videos/\${videoSlug}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the video status');
    });
}

function executeVideoDelete(videoSlug) {
    fetch(`/admin/videos/\${videoSlug}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the video');
    });
}
</script>
{% endblock %}
", "admin/video/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\video\\index.html.twig");
    }
}


/* admin/video/index.html.twig */
class __TwigTemplate_2b3e38b456dd2d5f7681233ce40ad52c___1856916277 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'table_content' => [$this, 'block_table_content'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 54
        return "components/admin_page_layout.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/index.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "admin/video/index.html.twig"));

        $this->parent = $this->load("components/admin_page_layout.html.twig", 54);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 55
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_table_content(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "table_content"));

        // line 56
        yield "
        <!-- Standardized Table -->
        ";
        // line 58
        $context["table_headers"] = [["text" => "Thumbnail"], ["text" => "Title"], ["text" => "Category"], ["text" => "Source"], ["text" => "Access Level"], ["text" => "Price"], ["text" => "Status"], ["text" => "Actions", "style" => "width: 200px;"]];
        // line 68
        yield "
        ";
        // line 69
        $context["table_rows"] = [];
        // line 70
        yield "        ";
        $context['_parent'] = $context;
        $context['_seq'] = CoreExtension::ensureTraversable((isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 70, $this->source); })()));
        foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
            // line 71
            yield "            ";
            $context["row_cells"] = [["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 73
$context["video"], "thumbnail", [], "any", false, false, false, 73)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((((("<img src=\"/uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source,             // line 74
$context["video"], "thumbnail", [], "any", false, false, false, 74)) . "\" alt=\"") . CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 74)) . "\" class=\"img-thumbnail\" style=\"width: 60px; height: 40px; object-fit: cover;\">")) : ("<div class=\"bg-light d-flex align-items-center justify-content-center\" style=\"width: 60px; height: 40px; border-radius: 4px;\"><i class=\"fas fa-video text-muted\"></i></div>"))], ["content" => (("<h6 class=\"video-title mb-0 font-weight-bold text-dark\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 78
$context["video"], "title", [], "any", false, false, false, 78)) . "</h6>")], ["content" => (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 81
$context["video"], "category", [], "any", false, false, false, 81)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ((("<span class=\"badge bg-info\">" . CoreExtension::getAttribute($this->env, $this->source,             // line 82
$context["video"], "category", [], "any", false, false, false, 82)) . "</span>")) : ("<span class=\"text-muted\">No category</span>"))], ["content" => (((CoreExtension::getAttribute($this->env, $this->source,             // line 87
$context["video"], "videoSourceType", [], "any", false, false, false, 87) == "youtube")) ? ("<span class=\"badge bg-danger\"><i class=\"fab fa-youtube mr-1\"></i>YouTube</span>") : ((((CoreExtension::getAttribute($this->env, $this->source,             // line 88
$context["video"], "videoSourceType", [], "any", false, false, false, 88) == "vdocipher")) ? ("<span class=\"badge bg-success\"><i class=\"fas fa-shield-alt mr-1\"></i>VdoCipher</span>") : ("<span class=\"badge bg-primary\"><i class=\"fas fa-upload mr-1\"></i>Upload</span>"))))], ["content" => (((CoreExtension::getAttribute($this->env, $this->source,             // line 93
$context["video"], "accessLevel", [], "any", false, false, false, 93) == "public_free")) ? ("<span class=\"badge bg-success\"><i class=\"fas fa-globe mr-1\"></i>Public Free</span>") : ((((CoreExtension::getAttribute($this->env, $this->source,             // line 94
$context["video"], "accessLevel", [], "any", false, false, false, 94) == "login_required_free")) ? ("<span class=\"badge bg-info\"><i class=\"fas fa-user mr-1\"></i>Login Required</span>") : ("<span class=\"badge bg-warning\"><i class=\"fas fa-crown mr-1\"></i>Premium</span>"))))], ["content" => CoreExtension::getAttribute($this->env, $this->source,             // line 98
$context["video"], "formattedPrice", [], "any", false, false, false, 98)], ["content" => (((("<span class=\"badge " . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 101
$context["video"], "isActive", [], "any", false, false, false, 101)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("bg-success") : ("bg-secondary"))) . "\">") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isActive", [], "any", false, false, false, 101)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Active") : ("Inactive"))) . "</span>")], ["content" => (((((((((((((((((((((("<div class=\"btn-group\" role=\"group\">
                        <a href=\"" . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_show", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 105
$context["video"], "id", [], "any", false, false, false, 105)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Video\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"") . $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("admin_video_edit", ["id" => CoreExtension::getAttribute($this->env, $this->source,             // line 106
$context["video"], "id", [], "any", false, false, false, 106)])) . "\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Video\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 107
$context["video"], "isActive", [], "any", false, false, false, 107)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#6c757d") : ("#28a745"))) . " 0%, ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isActive", [], "any", false, false, false, 107)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("#5a6268") : ("#1e7e34"))) . " 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isActive", [], "any", false, false, false, 107)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("Deactivate") : ("Activate"))) . " Video\" onclick=\"toggleVideoStatus('") . CoreExtension::getAttribute($this->env, $this->source, $context["video"], "slug", [], "any", false, false, false, 107)) . "', '") . $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 107), "js")) . "', ") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isActive", [], "any", false, false, false, 107)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("true") : ("false"))) . ")\"><i class=\"fas fa-") . (((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "isActive", [], "any", false, false, false, 107)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ("pause") : ("play"))) . "\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Video\" onclick=\"deleteVideo('") . CoreExtension::getAttribute($this->env, $this->source,             // line 108
$context["video"], "slug", [], "any", false, false, false, 108)) . "', '") . $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 108), "js")) . "')\"><i class=\"fas fa-trash\"></i></button>
                    </div>")]];
            // line 112
            yield "            ";
            $context["table_rows"] = Twig\Extension\CoreExtension::merge((isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 112, $this->source); })()), [["cells" => (isset($context["row_cells"]) || array_key_exists("row_cells", $context) ? $context["row_cells"] : (function () { throw new RuntimeError('Variable "row_cells" does not exist.', 112, $this->source); })()), "class" => "video-row"]]);
            // line 113
            yield "        ";
        }
        $_parent = $context['_parent'];
        unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
        $context = array_intersect_key($context, $_parent) + $_parent;
        // line 114
        yield "
        ";
        // line 115
        yield from $this->load("components/admin_table.html.twig", 115)->unwrap()->yield(CoreExtension::merge($context, ["headers" =>         // line 116
(isset($context["table_headers"]) || array_key_exists("table_headers", $context) ? $context["table_headers"] : (function () { throw new RuntimeError('Variable "table_headers" does not exist.', 116, $this->source); })()), "rows" =>         // line 117
(isset($context["table_rows"]) || array_key_exists("table_rows", $context) ? $context["table_rows"] : (function () { throw new RuntimeError('Variable "table_rows" does not exist.', 117, $this->source); })()), "row_class" => "video-row", "empty_message" => "No videos found", "empty_icon" => "fas fa-video", "empty_description" => "Get started by creating your first video.", "search_config" => ["fields" => [".video-title"]]]));
        // line 126
        yield "    ";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "admin/video/index.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  655 => 126,  653 => 117,  652 => 116,  651 => 115,  648 => 114,  642 => 113,  639 => 112,  636 => 108,  634 => 107,  632 => 106,  630 => 105,  628 => 101,  627 => 98,  626 => 94,  625 => 93,  624 => 88,  623 => 87,  622 => 82,  621 => 81,  620 => 78,  619 => 74,  618 => 73,  616 => 71,  611 => 70,  609 => 69,  606 => 68,  604 => 58,  600 => 56,  587 => 55,  564 => 54,  191 => 133,  178 => 132,  165 => 128,  163 => 54,  160 => 53,  158 => 46,  157 => 39,  156 => 32,  155 => 25,  154 => 13,  141 => 12,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'admin/base.html.twig' %}

{% block title %}Video Management - Capitol Academy Admin{% endblock %}

{% block page_title %}Video Management{% endblock %}

{% block breadcrumbs %}
<li class=\"breadcrumb-item\"><a href=\"{{ path('admin_dashboard') }}\">Home</a></li>
<li class=\"breadcrumb-item active\">Videos</li>
{% endblock %}

{% block content %}
{% set page_config = {
    'page_title': 'Video Management',
    'page_icon': 'fas fa-video',
    'search_placeholder': 'Search videos by title, category, or description...',
    'create_button': {
        'url': path('admin_video_new'),
        'text': 'Add New Video',
        'icon': 'fas fa-plus'
    },
    'stats': [
        {
            'title': 'Total Videos',
            'value': videos|length,
            'icon': 'fas fa-video',
            'color': '#1e3c72',
            'gradient': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
        },
        {
            'title': 'Free Videos',
            'value': videos|filter(video => video.isFree)|length,
            'icon': 'fas fa-unlock',
            'color': '#28a745',
            'gradient': 'linear-gradient(135deg, #28a745 0%, #20c997 100%)'
        },
        {
            'title': 'Premium Videos',
            'value': videos|filter(video => not video.isFree)|length,
            'icon': 'fas fa-crown',
            'color': '#ffc107',
            'gradient': 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)'
        },
        {
            'title': 'Active Videos',
            'value': videos|filter(video => video.isActive)|length,
            'icon': 'fas fa-play-circle',
            'color': '#a90418',
            'gradient': 'linear-gradient(135deg, #a90418 0%, #8b0314 100%)'
        }
    ]
} %}

{% embed 'components/admin_page_layout.html.twig' with page_config %}
    {% block table_content %}

        <!-- Standardized Table -->
        {% set table_headers = [
            {'text': 'Thumbnail'},
            {'text': 'Title'},
            {'text': 'Category'},
            {'text': 'Source'},
            {'text': 'Access Level'},
            {'text': 'Price'},
            {'text': 'Status'},
            {'text': 'Actions', 'style': 'width: 200px;'}
        ] %}

        {% set table_rows = [] %}
        {% for video in videos %}
            {% set row_cells = [
                {
                    'content': video.thumbnail ?
                        '<img src=\"/uploads/videos/thumbnails/' ~ video.thumbnail ~ '\" alt=\"' ~ video.title ~ '\" class=\"img-thumbnail\" style=\"width: 60px; height: 40px; object-fit: cover;\">' :
                        '<div class=\"bg-light d-flex align-items-center justify-content-center\" style=\"width: 60px; height: 40px; border-radius: 4px;\"><i class=\"fas fa-video text-muted\"></i></div>'
                },
                {
                    'content': '<h6 class=\"video-title mb-0 font-weight-bold text-dark\">' ~ video.title ~ '</h6>'
                },
                {
                    'content': video.category ?
                        '<span class=\"badge bg-info\">' ~ video.category ~ '</span>' :
                        '<span class=\"text-muted\">No category</span>'
                },
                {
                    'content':
                        (video.videoSourceType == 'youtube') ? '<span class=\"badge bg-danger\"><i class=\"fab fa-youtube mr-1\"></i>YouTube</span>' :
                        (video.videoSourceType == 'vdocipher') ? '<span class=\"badge bg-success\"><i class=\"fas fa-shield-alt mr-1\"></i>VdoCipher</span>' :
                        '<span class=\"badge bg-primary\"><i class=\"fas fa-upload mr-1\"></i>Upload</span>'
                },
                {
                    'content':
                        (video.accessLevel == 'public_free') ? '<span class=\"badge bg-success\"><i class=\"fas fa-globe mr-1\"></i>Public Free</span>' :
                        (video.accessLevel == 'login_required_free') ? '<span class=\"badge bg-info\"><i class=\"fas fa-user mr-1\"></i>Login Required</span>' :
                        '<span class=\"badge bg-warning\"><i class=\"fas fa-crown mr-1\"></i>Premium</span>'
                },
                {
                    'content': video.formattedPrice
                },
                {
                    'content': '<span class=\"badge ' ~ (video.isActive ? 'bg-success' : 'bg-secondary') ~ '\">' ~ (video.isActive ? 'Active' : 'Inactive') ~ '</span>'
                },
                {
                    'content': '<div class=\"btn-group\" role=\"group\">
                        <a href=\"' ~ path('admin_video_show', {'id': video.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"View Video\"><i class=\"fas fa-eye\"></i></a>
                        <a href=\"' ~ path('admin_video_edit', {'id': video.id}) ~ '\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"Edit Video\"><i class=\"fas fa-edit\"></i></a>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, ' ~ (video.isActive ? '#6c757d' : '#28a745') ~ ' 0%, ' ~ (video.isActive ? '#5a6268' : '#1e7e34') ~ ' 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem; margin-right: 2px;\" title=\"' ~ (video.isActive ? 'Deactivate' : 'Activate') ~ ' Video\" onclick=\"toggleVideoStatus(\\'' ~ video.slug ~ '\\', \\'' ~ video.title|e('js') ~ '\\', ' ~ (video.isActive ? 'true' : 'false') ~ ')\"><i class=\"fas fa-' ~ (video.isActive ? 'pause' : 'play') ~ '\"></i></button>
                        <button type=\"button\" class=\"btn btn-sm shadow-sm\" style=\"background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; border-radius: 6px; padding: 0.5rem 0.75rem;\" title=\"Delete Video\" onclick=\"deleteVideo(\\'' ~ video.slug ~ '\\', \\'' ~ video.title|e('js') ~ '\\')\"><i class=\"fas fa-trash\"></i></button>
                    </div>'
                }
            ] %}
            {% set table_rows = table_rows|merge([{'cells': row_cells, 'class': 'video-row'}]) %}
        {% endfor %}

        {% include 'components/admin_table.html.twig' with {
            'headers': table_headers,
            'rows': table_rows,
            'row_class': 'video-row',
            'empty_message': 'No videos found',
            'empty_icon': 'fas fa-video',
            'empty_description': 'Get started by creating your first video.',
            'search_config': {
                'fields': ['.video-title']
            }
        } %}
    {% endblock %}
{% endembed %}


{% endblock %}

{% block javascripts %}
<script>
// Enhanced confirmation modals using admin_page_layout component
function showStatusModal(itemName, currentStatus, callback) {
    const action = currentStatus === true ? 'deactivate' : 'activate';
    document.getElementById('statusAction').textContent = action;
    document.getElementById('itemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmStatusToggle');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('statusToggleModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('statusToggleModal')).show();
}

function showDeleteModal(itemName, callback) {
    document.getElementById('deleteItemTitle').textContent = itemName;

    const confirmBtn = document.getElementById('confirmDelete');
    confirmBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
        modal.hide();
        callback();
    };

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// Video management functions
function toggleVideoStatus(videoSlug, videoTitle, currentStatus) {
    showStatusModal(videoTitle, currentStatus, function() {
        executeVideoStatusToggle(videoSlug);
    });
}

function deleteVideo(videoSlug, videoTitle) {
    showDeleteModal(videoTitle, function() {
        executeVideoDelete(videoSlug);
    });
}

// Actual execution functions
function executeVideoStatusToggle(videoSlug) {
    fetch(`/admin/videos/\${videoSlug}/toggle-status`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the video status');
    });
}

function executeVideoDelete(videoSlug) {
    fetch(`/admin/videos/\${videoSlug}/delete`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the video');
    });
}
</script>
{% endblock %}
", "admin/video/index.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\admin\\video\\index.html.twig");
    }
}
