<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_ServiceLocator_IRNccQMService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.service_locator.iRNccQM' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ServiceLocator
     */
    public static function do($container, $lazyLoad = true)
    {
        return $container->privates['.service_locator.iRNccQM'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($container->getService ??= $container->getService(...), [
            'analyticsService' => ['privates', '.errored.SwfxRne', NULL, 'Cannot determine controller argument for "App\\Controller\\Admin\\VideoController::analytics()": the $analyticsService argument is type-hinted with the non-existent class or interface: "App\\Service\\VideoAnalyticsService".'],
        ], [
            'analyticsService' => '?',
        ]);
    }
}
