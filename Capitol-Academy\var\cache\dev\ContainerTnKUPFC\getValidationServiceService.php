<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class getValidationServiceService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private 'App\Service\ValidationService' shared autowired service.
     *
     * @return \App\Service\ValidationService
     */
    public static function do($container, $lazyLoad = true)
    {
        include_once \dirname(__DIR__, 4).''.\DIRECTORY_SEPARATOR.'src'.\DIRECTORY_SEPARATOR.'Service'.\DIRECTORY_SEPARATOR.'ValidationService.php';

        return $container->privates['App\\Service\\ValidationService'] = new \App\Service\ValidationService(($container->privates['debug.validator'] ?? self::getDebug_ValidatorService($container)));
    }
}
