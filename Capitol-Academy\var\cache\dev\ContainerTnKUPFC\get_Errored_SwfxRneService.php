<?php

namespace ContainerTnKUPFC;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

/**
 * @internal This class has been auto-generated by the Symfony Dependency Injection Component.
 */
class get_Errored_SwfxRneService extends App_KernelDevDebugContainer
{
    /**
     * Gets the private '.errored.SwfxRne' shared service.
     *
     * @return \App\Service\VideoAnalyticsService
     */
    public static function do($container, $lazyLoad = true)
    {
        throw new RuntimeException('Cannot determine controller argument for "App\\Controller\\Admin\\VideoController::analytics()": the $analyticsService argument is type-hinted with the non-existent class or interface: "App\\Service\\VideoAnalyticsService".');
    }
}
